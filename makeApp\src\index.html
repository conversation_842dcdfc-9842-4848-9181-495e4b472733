<!DOCTYPE html>
<html lang="zh-CN">
<head>
	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<title>ComfyUI工作流加工坊</title>
	<!-- 引入Element Plus样式 -->
	<link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
	<!-- 引入Vue3和Element Plus -->
	<script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
	<script src="https://unpkg.com/element-plus"></script>
	<!-- 引入Element Plus图标 -->
	<script src="https://unpkg.com/@element-plus/icons-vue"></script>
	<style>
		* { margin: 0; padding: 0; box-sizing: border-box; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
		body { background-color: #f5f7fa; color: #333; padding: 20px; }
		.container { max-width: 1600px; margin: 0 auto; }
		.header { background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%); color: white; padding: 20px; border-radius: 12px; box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15); margin-bottom: 24px; text-align: center; }
		.upload-container { max-width: 800px; margin: 0 auto; padding: 30px; background: rgba(255, 255, 255, 0.2); backdrop-filter: blur(10px); border-radius: 10px; }
		.main-content { display: flex; gap: 24px; min-height: 70vh;}
		.all-panel, .editor-panel, .image-panel { flex: 1; background: white; padding: 20px; border-radius: 12px; box-shadow: 0 6px 16px rgba(0, 0, 0, 0.08); }
		.editor-panel{ flex: 2;  display: flex;flex-direction: column;position: relative; }
		.panel-title { font-size: 1.4rem; color: #2c3e50; margin-bottom: 20px; padding-bottom: 12px; border-bottom: 2px solid #eaeaea; display: flex; align-items: center; }
		.panel-title i { margin-right: 10px; color: #fff; }
		.sm-title { font-size: 1rem; color: #9fa5b0; margin-left: 15px;margin-top: 5px; }
		.right-title{
			display: flex;
			justify-content: space-between;
		}
		/* 二级菜单样式 */
		.collapse-container { margin-bottom: 20px; }
		.collapse-header { display: flex; justify-content: space-between; align-items: center; padding: 12px; background: #f8f9ff; border-radius: 8px; cursor: pointer; transition: all 0.3s ease; }
		.collapse-header:hover { background: #eef2ff; }
		.collapse-title { font-weight: 600; color: #2c3e50; }
		.collapse-icon { transition: transform 0.3s; }
		.collapse-icon.open { transform: rotate(180deg); }
		.collapse-content { padding: 15px; background: #fdfdfd; border-radius: 0 0 8px 8px; border: 1px solid #eee; border-top: none; }

		.json-item { display: flex; margin-bottom: 16px; padding: 12px; background: #f8f9ff; border-radius: 8px; transition: all 0.3s ease; }
		.json-item:hover { background: #eef2ff; transform: translateY(-2px); box-shadow: 0 4px 8px rgba(106, 17, 203, 0.1); }
		.all-panel .json-item:hover { background: linear-gradient(90deg, #CAD5FA 0%, #B1C0F0 80%); transform: translateY(-2px); box-shadow: 0 4px 8px rgba(106, 17, 203, 0.1); }
		.json-selected-item{
			background: linear-gradient(90deg, #CAD5FA 0%, #B1C0F0 80%);
			box-shadow: 0 3px 12px rgba(106, 127, 240, 0.12);
			transform: translateY(-2px);
		}
		.key-label { flex: 1; font-weight: 600; color: #2c3e50; display: flex; align-items: center; font-size: 0.95rem; }
		.value-input { flex: 1; }
		.x-content{ display: flex;
			justify-content: center; /* 水平居中 */
			align-items: center;     /* 垂直居中 */
			width: 40px;
			cursor: pointer;
			color: #c03639;
		}
		.edit-right-container{flex:1}
		.button-group {
			margin-top: auto; /* 将按钮组推到容器的底部 */
		}
		.button-group { margin-top: 24px; display: flex; justify-content: center; gap: 15px; }
		.button-group i { margin-right: 10px; color: #fff; }
		.image-container { display: flex; justify-content: center; align-items: center; min-height: 400px; }
		.image-preview { max-width: 100%; max-height: 500px; border-radius: 8px; box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1); border: 1px solid #eee; }
		.no-data { text-align: center; padding: 40px; color: #9e9e9e; }
		.no-data i { font-size: 3rem; color: #e0e0e0; margin-bottom: 15px; }
		.footer { text-align: right; }
		@media (max-width: 992px) { .main-content { flex-direction: column; } }
		.edit-right-content{min-height: 55vh;}

		.collapse-enter-active,
		.collapse-leave-active {
			transition:
					height 0.01s cubic-bezier(0.645, 0.045, 0.355, 1),
					opacity 0.01s ease;
			overflow: hidden;
		}
		.flex-content {
			display: flex;
			flex-wrap: wrap;
		}
		.flex-content div {
			width: calc(50% - 10px);/* 每个子元素占据50%宽度 */
			box-sizing: border-box; /* 确保内边距和边框不影响宽度计算 */
			cursor: pointer;
		}
		.flex-content div:nth-child(odd) {
			margin-right: 20px;
		}
		/* 别名编辑按钮样式 */
		.alias-edit {
			margin-left: 8px;
			cursor: pointer;
			color: #606266;
			transition: color 0.3s;
		}
		.alias-edit:hover {
			color: #409eff;
		}
	</style>
</head>
<body>
<div id="app">
	<div class="container">
		<div class="header">
			<h1>ComfyUI工作流加工坊</h1>
			<div class="upload-container">
				<el-upload
						action="#"
						:auto-upload="false"
						:show-file-list="false"
						:on-change="handleFileChange"
						accept=".json"
				>
					<template #trigger>
						<el-button type="primary" size="large">
							<el-icon><Upload /></el-icon> 点击上传JSON文件
						</el-button>
					</template>
					<template #tip>
						<div style="margin-top: 10px; color: rgba(255,255,255,0.8)">
							{{abstractData}}
						</div>
					</template>
				</el-upload>
			</div>
		</div>

		<div class="main-content">
			<div class="all-panel">
				<div class="panel-title">
					<span>所有可编辑节点</span>
					<span class="sm-title">点击节点到右侧编辑</span>
				</div>

				<template v-if="jsonData.length">
					<div v-for="(group, groupIndex) in leftGroupedData" :key="groupIndex" class="collapse-container" >
						<div
								class="collapse-header"
								@click="toggleLeftCollapse(groupIndex)"
						>
							<div class="collapse-title">
								{{ group.groupKey }}
								<span v-if="group.alias">({{ group.alias }})</span>
							</div>
							<el-icon class="collapse-icon" :class="{ open: group.isOpen }">
								<arrow-down />
							</el-icon>
						</div>
						<!-- 添加过渡效果 -->
						<transition
								name="collapse"
								@enter="enter"
								@after-enter="afterEnter"
								@leave="leave"
								@after-leave="afterLeave"
						>
							<div class="collapse-content flex-content" v-show="group.isOpen">
								<div v-for="(item, itemIndex) in group.items"
									 :key="itemIndex"
									 class="json-item"
									 :class="{ 'json-selected-item': item.editable }"
									 @click="()=>item.editable = true">
									<div class="key-label">
										{{ item.field }}
										<span v-if="item.alias">({{ item.alias }})</span>
									</div>
								</div>
							</div>
						</transition>
					</div>
				</template>

				<div v-else class="no-data">
					<el-icon><document /></el-icon>
					<p>请上传JSON文件以开始编辑</p>
				</div>
			</div>
			<div class="editor-panel">
				<div class="panel-title right-title">
					<div>
						<span>已选择的修改节点</span>
						<span class="sm-title">点击X取消编辑,并还原节点</span>
					</div>
				</div>
				<div class="edit-right-content">
					<template v-if="jsonData.length">
						<div v-for="(group, groupIndex) in rightGroupedData" :key="groupIndex" :class="{ 'collapse-container': group.items.some(item => item.editable) }" >
							<div
									class="collapse-header"
									@click="toggleRightCollapse(groupIndex)"
									v-if="group.items.some(item => item.editable)"
							>
								<div class="collapse-title">
									{{ group.groupKey }}
									<span v-if="group.alias">({{ group.alias }})</span>
									<!-- 添加分组别名编辑按钮 -->
									<el-icon class="alias-edit" @click.stop="editGroupAlias(group)">
										<edit />
									</el-icon>
								</div>
								<el-icon class="collapse-icon" :class="{ open: group.isOpen }">
									<arrow-down />
								</el-icon>
							</div>
							<!-- 添加过渡效果 -->
							<transition
									name="collapse"
									@enter="enter"
									@after-enter="afterEnter"
									@leave="leave"
									@after-leave="afterLeave"
							>
								<div class="collapse-content"  v-if="group.isOpen && group.items.some(item => item.editable)">
									<template v-for="(item, itemIndex) in group.items" :key="itemIndex">
										<div v-if="item.editable" class="json-item">
											<div class="key-label">
												{{ item.field }}
												<span v-if="item.alias">({{ item.alias }})</span>
												<!-- 添加节点别名编辑按钮 -->
												<el-icon class="alias-edit" @click.stop="editItemAlias(item)">
													<edit />
												</el-icon>
											</div>
											<div class="value-input">
												<el-input
														v-model="item.value"
														size="large"
												/>
											</div>
											<div class="x-content" @click="handleValueChange(item)">
												X
											</div>
										</div>
									</template>
								</div>
							</transition>
						</div>
					</template>
					<div v-else class="no-data">
						<el-icon><document /></el-icon>
						<p>请上传JSON文件以开始编辑</p>
					</div>
				</div>
				<div v-if="jsonData.length" class="footer">
					<el-affix position="bottom" :offset="20">
						<el-button
								type="success"
								size="large"
								@click="downloadJson"
						>
							<template #default>
								<el-icon><download /></el-icon>
								保存并下载JSON文件
							</template>
						</el-button>
						<el-button
								type="primary"
								size="large"
								@click="submitJsonData"
								:loading="submitting"
						>
							<template #default>
								<el-icon><Upload /></el-icon>
								提交JSON数据,预览图片
							</template>
						</el-button>
					</el-affix>
				</div>
			</div>
		</div>
	</div>
	<el-dialog v-model="dialogVisible" title="预览" width="500">
		<el-image class="image-preview":src="base64Image" :fit="fit" />
		<template #footer>
			<div class="dialog-footer">
				<el-button type="primary" @click="dialogVisible = false">
					好的
				</el-button>
			</div>
		</template>
	</el-dialog>
</div>

<script>
	const { createApp, ref, reactive,computed  } = Vue;
	const app = createApp({
		setup() {
			//url
			const url = ref('http://*************:8189');
			// 示例JSON数据
			const jsonData = ref([]);
			// 示例JSON数据
			const dialogVisible = ref(false);
			// 原始JSON结构
			const originalJson = ref({});
			// Base64图片数据
			const base64Image = ref('');
			// 提交状态
			const submitting = ref(false);
			// 为左右面板分别创建折叠状态变量
			const leftCollapseState = ref({});
			const rightCollapseState = ref({});
			// 原始JSON结构
			const originalData = ref();
			const abstractData = ref('仅支持JSON格式文件，最大5MB');
			const groupAliases = ref({});
			//处理点击事件
			const handleValueChange = (item) => {
				// 这里可以添加处理值变化的逻辑，例如更新原始JSON数据等。
				item.editable = false;
				item.value = originalData.value[item.node].inputs[item.field];
			};
			const getInfo = (data) => {
				fetch(url.value + '/upload-json-to-get-node-description-lazy', {
					method: 'POST',
					// 注意：不要设置Content-Type头，让浏览器自动设置
					body: data
				}).then(response => {
					if (!response.ok) {
						throw new Error('服务器响应错误');
					}
					return response.json();
				}).then(data => {
					jsonData.value.forEach(item => {
						item.info = data.data.nodes_description[item.groupKey+'-'+item.field];
					});
				}).catch(error => {
					ElementPlus.ElMessage.error(error.message);
				})
			};
			const fullText = ref('')
			const currentIndex = ref(0)
			let typingTimer = null
			const typeNextCharacter = () => {
				if (currentIndex.value < fullText.value.length) {
					abstractData.value += fullText.value[currentIndex.value]
					currentIndex.value++

					// 设置下一个字符的显示时间，可根据需要调整速度
					typingTimer = setTimeout(() => {
						typeNextCharacter()
					}, 150)
				} else {
					// 所有字符都已显示完毕
					typingTimer = null
				}
			}
			const getInfoStream =async (data) => {
				try {
					const response = await fetch(url.value + '/upload-json-to-get-summary-stream', {
						method: 'POST',
						// 注意：不要设置Content-Type头，让浏览器自动设置
						body: data
					})

					if (!response.ok) {
						throw new Error(`HTTP error! status: ${response.status}`)
					}

					const reader = response.body.getReader()
					const decoder = new TextDecoder()

					while (true) {
						const { done, value } = await reader.read()

						if (done) {
							console.log('流读取完成')
							break
						}

						const chunk = decoder.decode(value, { stream: true })
						fullText.value += chunk

						// 如果没有正在进行的打字动画，则开始新的动画
						if (!typingTimer) {
							abstractData.value = '工作流解析：'
							typeNextCharacter()
						}
					}
				} catch (error) {
					console.error('获取流数据时出错:', error)
				}
			};

			// 处理文件上传 [8,10](@ref)
			const handleFileChange = (file) => {
				// 检查文件类型
				if (!file.raw.type.includes('json') && !file.name.endsWith('.json')) {
					ElementPlus.ElMessage.error('请上传JSON格式文件！');
					return;
				}

				// 检查文件大小
				if (file.size > 5 * 1024 * 1024) {
					ElementPlus.ElMessage.error('文件大小不能超过5MB！');
					return;
				}

				// 读取文件内容
				const reader = new FileReader();
				reader.onload = (e) => {
					try {
						// 创建FormData对象
						const formData = new FormData();
						// 添加文件到FormData（假设e.target.result是文件内容）
						formData.append('file', file.raw);
						// 使用Fetch API提交JSON数据
						fetch(url.value + '/upload-json', {
							method: 'POST',
							// 注意：不要设置Content-Type头，让浏览器自动设置
							body: formData
						}).then(response => {
							if (!response.ok) {
								throw new Error('服务器响应错误');
							}
							// getInfo(formData);
							// getInfoStream(formData);
							return response.json();
						}).then(data => {
							originalJson.value = data.data.editable_nodes; // 保存原始结构
							originalData.value = JSON.parse(e.target.result); // 保存原始数据
							parseJsonData(data.data.editable_nodes, data.data.extro_info);
						}).catch(error => {
							ElementPlus.ElMessage.error(error.message);
						})
					} catch (error) {
						ElementPlus.ElMessage.error('JSON解析错误：' + error.message);
					}
				};
				reader.readAsText(file.raw);
			};

			// 解析JSON数据
			const parseJsonData = (data) => {
				const items = [];

				// 遍历JSON对象
				for (const key in data) {
					if (data[key] && data[key].editable_inputs) {
						const editableInputs = data[key].editable_inputs;

						// 遍历可编辑字段
						for (const field in editableInputs) {
							const value = editableInputs[field];

							// 跳过空值
							if (value === null || value === undefined) continue;

							const groupKey = `${key}-${data[key].class_type}`;

							items.push({
								node: key,
								classType: data[key].class_type,
								field: field,
								groupKey: groupKey,
								value: value,
								originalValue: value,
								editable: false,
								info:'正在加载...',
								alias: ''
							});
						}
					}
				}

				console.log(items)
				jsonData.value = items;
			};

			// 修改分组数据计算属性
			const leftGroupedData  = computed(() => {
				const groups = {};

				jsonData.value.forEach(item => {
					if (!groups[item.groupKey]) {
						groups[item.groupKey] = {
							groupKey: item.groupKey,
							items: [],
							isOpen: leftCollapseState.value[item.groupKey] || false,
							alias: groupAliases.value[item.groupKey] || "", // 从新数据源读取
						};
					}
					groups[item.groupKey].items.push(item);
				});

				return Object.values(groups);
			});

			const rightGroupedData = computed(() => {
				const groups = {};
				jsonData.value.forEach(item => {
					if (!groups[item.groupKey]) {
						groups[item.groupKey] = {
							groupKey: item.groupKey,
							items: [],
							isOpen: rightCollapseState.value[item.groupKey] || false,
							alias: groupAliases.value[item.groupKey] || "", // 从新数据源读取
						};
					}
					groups[item.groupKey].items.push(item);
				});
				return Object.values(groups);
			});
			// 修改折叠切换函数
			const toggleLeftCollapse = (groupKey) => {
				const group = leftGroupedData.value[groupKey];
				leftCollapseState.value[group.groupKey] = !group.isOpen;
				rightCollapseState.value[group.groupKey] = true;
			};

			const toggleRightCollapse = (groupKey) => {
				const group = rightGroupedData.value[groupKey];
				rightCollapseState.value[group.groupKey] = !group.isOpen;
			};

			// 修改toggleCollapse函数，用于处理动画（可选）
			const toggleCollapse = (el, isOpen) => {
				el.style.height = isOpen ? `${el.scrollHeight}px` : '0';
				el.style.opacity = isOpen ? '1' : '0';
			};

			// 提交JSON数据到服务器 [4,9](@ref)
			const submitJsonData = () => {
				submitting.value = true;
				// 创建修改后的JSON结构
				const modifiedJson = {...originalJson.value};
				// 应用用户修改
				// jsonData.value.forEach(item => {
				// 	let node = rightGroupedData.value.find(v => v.groupKey === item.groupKey)
				// 	if(node){
				// 		modifiedJson[item.node].nodeAlias = node.alias;
				// 	}
				// 	if (modifiedJson[item.node]) {
				// 		const nodeData = modifiedJson[item.node];
				//
				// 		// 确保 editable_inputs 和 aliases 对象存在
				// 		nodeData.editable_inputs = nodeData.editable_inputs || {};
				// 		nodeData.aliases = nodeData.aliases || {};
				//
				// 		// 处理字段值（保持原有逻辑，不再拼接别名）
				// 		nodeData.editable_inputs[item.field] =
				// 				typeof item.originalValue === 'number' ? Number(item.value) : item.value;
				//
				// 		// 处理别名映射：仅在 alias 存在时添加/更新映射
				// 		if (item.alias) {
				// 			nodeData.aliases[item.field] = item.alias;
				// 		} else {
				// 			// 若 alias 不存在且旧映射存在，删除该映射
				// 			delete nodeData.aliases[item.field];
				// 		}
				// 	}
				// });

				jsonData.value.forEach(item => {
					if (modifiedJson[item.node] && modifiedJson[item.node].editable_inputs) {
						modifiedJson[item.node].editable_inputs[item.field] =
								typeof item.originalValue === 'number' ?
										Number(item.value) : item.value;
					}
				});
				// 使用Fetch API提交JSON数据
				fetch(url.value + '/generate', {
					method: 'POST',
					headers: {
						'Content-Type': 'application/json'
					},
					body: JSON.stringify({
						editable_nodes: modifiedJson,
						origin_workflow: originalData.value
					})
				})
						.then(response => {
							if (!response.ok) {
								throw new Error('服务器响应错误,请重试');
							}
							return response.json();
						})
						.then(data => {
							ElementPlus.ElMessage.success('提交成功！');
							// 处理服务器返回的图片数据
							if (data.data.image_base64) {
								// 判断返回的是否是完整的数据URI
								if (data.data.image_base64.startsWith('data:image')) {
									base64Image.value = data.data.image_base64;
								} else {
									// 如果不是完整URI，添加前缀[1,2](@ref)
									base64Image.value = 'data:image/png;base64,' + data.data.image_base64;
								}
							}
							dialogVisible.value = true;
						})
						.catch(error => {
							ElementPlus.ElMessage.error(error.message);
						})
						.finally(() => {
							submitting.value = false;
						});
			};
			// 	下载JSON文件
			const downloadJson = () => {
				// 创建修改后的JSON结构
				const modifiedJson = {...originalJson.value};
				// // 应用用户修改
				// jsonData.value.forEach(item => {
				// 	if (modifiedJson[item.node] && modifiedJson[item.node].editable_inputs) {
				// 		modifiedJson[item.node].editable_inputs[item.field] =
				// 				typeof item.originalValue === 'number' ?
				// 						Number(item.value) : item.value;
				// 	}
				// });
				// 应用用户修改
				jsonData.value.forEach(item => {
					let node = rightGroupedData.value.find(v => v.groupKey === item.groupKey)
					if(node){
						modifiedJson[item.node].nodeAlias = node.alias;
					}
					if (modifiedJson[item.node]) {
						const nodeData = modifiedJson[item.node];

						// 确保 editable_inputs 和 aliases 对象存在
						nodeData.editable_inputs = nodeData.editable_inputs || {};
						nodeData.aliases = nodeData.aliases || {};

						// 处理字段值（保持原有逻辑，不再拼接别名）
						nodeData.editable_inputs[item.field] =
								typeof item.originalValue === 'number' ? Number(item.value) : item.value;

						// 处理别名映射：仅在 alias 存在时添加/更新映射
						if (item.alias) {
							nodeData.aliases[item.field] = item.alias;
						} else {
							// 若 alias 不存在且旧映射存在，删除该映射
							delete nodeData.aliases[item.field];
						}
					}
				});
				fetch(url.value + '/get_config', {
					method: 'POST',
					headers: {
						'Content-Type': 'application/json'
					},
					body: JSON.stringify({
						editable_nodes: modifiedJson,
						origin_workflow: originalData.value
					})
				}).then(response => {
					if (!response.ok) {
						throw new Error('服务器响应错误');
					}
					return response.json();
				})
				.then(data => {
					ElementPlus.ElMessage.success('保存成功！');
					// 转换为JSON字符
					const jsonStr = JSON.stringify(data, null, 2);

					// 创建Blob对象
					const blob = new Blob([jsonStr], { type: 'application/json' });

					// 创建下载链接
					const url = URL.createObjectURL(blob);
					const a = document.createElement('a');
					a.href = url;
					a.download = 'modified-config.json';

					// 触发下载
					document.body.appendChild(a);
					a.click();

					// 清理
					setTimeout(() => {
						document.body.removeChild(a);
						URL.revokeObjectURL(url);
						ElementPlus.ElMessage.success('JSON文件下载成功！');
					}, 100);
				})
				.catch(error => {
					ElementPlus.ElMessage.error('保存失败: ' + error.message);
				})
			};

			// 替换原有的动画钩子函数
			const enter = (el) => {
				el.style.transition = 'height 0.01s cubic-bezier(0.645, 0.045, 0.355, 1), opacity 0.3s ease';
				el.style.height = '0';
				el.style.opacity = '0';

				requestAnimationFrame(() => {
					el.style.height = `${el.scrollHeight}px`;
					el.style.opacity = '1';
				});
			};

			const afterEnter = (el) => {
				el.style.height = '';
				el.style.opacity = '';
				el.style.transition = '';
			};

			const leave = (el) => {
				el.style.height = `${el.scrollHeight}px`;
				el.style.opacity = '1';

				requestAnimationFrame(() => {
					el.style.height = '0';
					el.style.opacity = '0';
				});
			};

			const afterLeave = (el) => {
				el.style.height = '';
				el.style.opacity = '';
			};
			// 在setup()中添加方法
			const editItemAlias = (item) => {
				ElementPlus.ElMessageBox.prompt('请输入节点别名', '节点别名', {
					confirmButtonText: '确定',
					cancelButtonText: '取消',
					inputValue: item.alias,
					inputPattern: /^.{0,20}$/,
					inputErrorMessage: '别名不能超过20个字符'
				}).then(({ value }) => {
					item.alias = value;
				}).catch(() => {});
			};

			const editGroupAlias = (group) => {
				ElementPlus.ElMessageBox.prompt('请输入分组别名', '分组别名', {
					confirmButtonText: '确定',
					cancelButtonText: '取消',
					inputValue: groupAliases.value[group.groupKey] || '', // 获取当前别名
					inputPattern: /^.{0,20}$/,
					inputErrorMessage: '别名不能超过20个字符'
				}).then(({ value }) => {
					// 更新响应式数据源
					groupAliases.value[group.groupKey] = value;
				}).catch(() => {});
			};
			return {
				enter,
				afterEnter,
				leave,
				afterLeave,
				jsonData,
				abstractData,
				leftCollapseState,
				rightCollapseState,
				leftGroupedData,
				rightGroupedData,
				base64Image,
				submitting,
				handleFileChange,
				toggleCollapse,
				submitJsonData,
				downloadJson,
				handleValueChange,
				dialogVisible,
				toggleLeftCollapse,
				toggleRightCollapse,
				editGroupAlias,
				editItemAlias,
			};
		}
	});

	// 注册Element Plus
	app.use(ElementPlus);

	// 在创建Vue应用后立即注册图标组件
	const { EditPen, Picture, Document, Upload, ArrowDown, Download,QuestionFilled,Loading ,Edit} = ElementPlusIconsVue;

	// 显式注册每个图标组件（修复大小写问题）
	app.component('edit-pen', EditPen);
	app.component('a-picture', Picture);
	app.component('document', Document);
	app.component('Upload', Upload);
	app.component('arrow-down', ArrowDown);
	app.component('download', Download);
	app.component('question-filled', QuestionFilled);
	app.component('loading', Loading);
	app.component('edit', Edit);

	app.mount('#app');
</script>
</body>
</html>