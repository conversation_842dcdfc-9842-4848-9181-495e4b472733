<template>
  <el-container class="full-container">
    <el-header class="header">
      <div class="logo-container">
        <img src="/@/assets/logo.png" alt="Logo" class="logo">
      </div>
      <div class="header-title">运行工作流应用</div>
      <div class="button-container">
        <el-badge is-dot :hidden="generatingTasks.length === 0 && queuedTasks.length === 0" class="item" :offset="[-8, 10]">
          <el-button type="primary" class="left-button" @click="openTaskDrawer">任务列表</el-button>
        </el-badge>
        <el-button type="success" class="right-button" @click="openMaterialDialog">素材库</el-button>
      </div>
    </el-header>
    <el-container>
      <!-- 左侧边栏：竖列按钮 -->
      <el-aside width="240px" class="left-aside">
        <el-scrollbar>
          <div class="button-list">
            <el-button
                v-for="(item,index) in workList"
                :key="item.id"
                class="sidebar-button"
                :class="{ 'sidebar-button-active': currentData && currentData.app_id === item.app_id }"
                @click="handleButtonClick(item,index)"
            >
              <span class="button-text-ellipsis">{{ item.app_name }}</span>
            </el-button>
          </div>
        </el-scrollbar>
      </el-aside>
      <template v-if="!currentData?.data">
        <el-main v-if="workList && workList.length !== 0">
          该应用暂时未有配置
        </el-main>
        <el-main v-else>
          暂无工作流应用，请先前往制作
        </el-main>
      </template>
      <template v-else>
        <div class="main-content-wrapper">
          <el-scrollbar>
            <div class="main-content">
              <template v-for="item in currentData?.data" :key="item.key" >
                <!-- 数据展示区域 -->
                <div v-if="item.type !== 'other'" class="content-item">
                  <h3>{{ item.alias || item.key }}</h3>
                  <!-- 数字输入框 -->
                  <div v-if="item.type === 'number'" class="input-wrapper">
                    <el-input-number
                        v-model="item.value"
                        :controls="false"
                        class="full-width"
                    />
                  </div>

                  <!-- 输入框 -->
                   <div v-else-if="item.type === 'string'" class="input-wrapper">
                    <el-input
                        v-model="item.value"
                        class="full-width"
                        :type="String(item.value).length > 20 ? 'textarea' : 'text'"
                        :ref="el => { if (el) inputRefs[item.key] = el; }"
                        @input="handleInput(item, $event)"
                    />
                  </div>
                  <!-- 图片上传框 -->
                  <div v-else-if="item.type === 'image'" class="input-wrapper">
                    <div class="image-upload-container">
                      <div v-if="item.value" class="image-preview-wrapper">
                        <el-image
                            :src="item.value"
                            fit="contain"
                            class="image-preview"
                            :preview-src-list="[item.value]"
                            preview-teleported
                        >
                          <template #error>
                            <div class="upload-placeholder">
                              <el-icon><Picture /></el-icon>
                            </div>
                          </template>
                        </el-image>
                      </div>
                      <div class="upload-placeholder" v-else>
                        <el-icon><Picture /></el-icon>
                        <span>点击上传图片</span>
                      </div>
                      <div class="image-actions">
                        <el-button @click="triggerImageUpload(item)" type="primary" size="small" class="upload-button">
                          {{ item.value ? '更换图片' : '选择图片' }}
                        </el-button>
                        <el-button
                            v-if="item.value"
                            @click="editImage(item)"
                            type="warning"
                            size="small"
                            class="edit-button"
                        >
                          编辑图片
                        </el-button>
                      </div>
                    </div>
                  </div>
                </div>
              </template>
            </div>
            <div class="main-footer">
              <div class="run-section">
                <el-button @click="runApp" type="primary" class="run-button">运行</el-button>
                <div class="times-input">
                  <el-input-number
                      v-model="num"
                      :min="1"
                      :max="100"
                      controls-position="right"
                  />
                  <span class="times-label">次</span>
                </div>
              </div>
            </div>
          </el-scrollbar>
        </div>
        <!-- 右侧边栏 -->
        <el-aside class="right-aside">
          <div class="info-section">
            <h3>应用名：{{ currentData?.app_name || '暂无' }}</h3>
            <p>详细信息：{{ currentData?.intro || '暂无' }}</p>
          </div>
          <div class="media-section">
            <el-carousel type="card"  v-if="mediaList && mediaList.length !== 0">
              <el-carousel-item v-for="item in mediaList" :key="item.id">
                <el-image
                    :src=" url + item.file_url"
                    fit="contain"
                    class="media-item"
                />
              </el-carousel-item>
            </el-carousel>
            <h3 v-else>
              运行后，此处将展示生成的内容
            </h3>
          </div>
        </el-aside>
      </template>
    </el-container>
  </el-container>

  <!-- 任务列表抽屉 -->
  <el-drawer
      v-model="drawerVisible"
      title="任务列表"
      direction="rtl"
      size="500px"
  >
    <div class="task-drawer-content">
      <!-- 生成中任务 -->
      <div class="task-section">
        <h3>生成中</h3>
        <div v-if="generatingTasks.length === 0" class="no-tasks">暂无任务</div>
        <div
            v-else
            v-for="task in generatingTasks"
            :key="task.id"
            class="task-item"
        >
          <span class="task-name">{{ task.app_name }}</span>
          <span class="task-count">{{ task.completed_prompt_count }}/{{task.execution_count}}</span>
        </div>
      </div>

      <!-- 排队中任务 -->
      <div class="task-section">
        <h3>排队中</h3>
        <div v-if="queuedTasks.length === 0" class="no-tasks">暂无任务</div>
        <div
            v-else
            v-for="task in queuedTasks"
            :key="task.id"
            class="task-item queued-task"
        >
          <span class="task-name">{{  task.app_name }}</span>
          <div class="queued-task-right">
            <span class="task-count">{{ task.completed_prompt_count }}/{{task.execution_count}}</span>
            <el-button
                v-if="false"
                type="danger"
                size="small"
                @click="deleteQueuedTask(task.id)"
                class="delete-button"
            >
              删除
            </el-button>
          </div>
        </div>
      </div>

      <!-- 已完成任务 -->
      <div class="task-section">
        <h3>已完成</h3>
        <div v-if="completedTasks.length === 0" class="no-tasks">暂无任务</div>
        <div
            v-else
            v-for="task in completedTasks"
            :key="task.id"
            class="task-item"
        >
          <span class="task-name">{{  task.app_name }}</span>
          <span class="task-count">{{ task.completed_prompt_count }}/{{task.execution_count}}</span>
        </div>
      </div>
    </div>
  </el-drawer>
  <!-- 素材库弹窗 -->
  <el-dialog
      v-model="materialDialogVisible"
      :title="currentTab===0?'素材库':'回收站'"
      width="80%"
      height="80%"
  >
    <div class="material-dialog-content">
      <template  v-for="folder in materialFolders" :key="folder.id" >
        <div v-if="folder.files && folder.files.length" class="folder-section">
          <h3>{{ folder.date_formatted }}
            <el-button
                type="primary"
                size="small"
                @click="selectAllInFolder(folder)"
                v-if="!areAllSelectedInFolder(folder)"
            >
              全选
            </el-button>
            <el-button
                type="warning"
                size="small"
                @click="deselectAllInFolder(folder)"
                v-else
            >
              取消全选
            </el-button></h3>
          <div class="media-grid">
            <div
                v-for="media in folder.files"
                :key="media.file_id"
                class="media-item"
                @click.stop="toggleMediaSelection(media.file_id)"
                @mouseenter="showZoomIcon(media.file_id)"
                @mouseleave="hideZoomIcon(media.file_id)"
            >
              <el-image
                  v-if="media.output_type === 'images'"
                  :src="media.thumbnail"
                  fit="cover"
                  class="media-thumbnail"
              />
              <video
                  v-else-if="media.output_type === 'video'"
                  :src="media.thumbnail"
                  class="media-thumbnail"
                  controls
              ></video>
              <div class="media-overlay" v-show="selectedMedia.includes(media.file_id)">
                <el-icon class="selected-icon">
                  <Check/>
                </el-icon>
              </div>
              <div class="zoom-icon" v-show="hoveredMediaId === media.file_id" @click="zoomMedia(media)">
                <el-icon>
                  <Search/>
                </el-icon>
              </div>
              <div class="select-checkbox" @click.stop="toggleMediaSelection(media.file_id)">
                <el-checkbox :model-value="selectedMedia.includes(media.file_id)"></el-checkbox>
              </div>
            </div>
          </div>
        </div>
      </template>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button  v-if="currentTab === 0" @click="moveToTrash">回收站</el-button>
        <el-button v-if="currentTab === 1"  @click="goBack">素材库</el-button>
        <el-button type="primary" @click="downloadSelected">批量下载</el-button>
        <el-button v-if="currentTab === 0" type="danger" @click="deleteSelected">删除</el-button>
      </span>
    </template>
  </el-dialog>
  <!-- 放大媒体对话框 -->
  <el-dialog
      v-model="zoomMediaDialogVisible"
      title="媒体预览"
      width="70%"
      top="5vh"
  >
    <div class="zoom-media-dialog-content">
      <el-image
          v-if="zoomMediaData.output_type === 'images'"
          :src=" url + zoomMediaData.file_url"
          fit="contain"
          class="zoom-media-image"
      />
      <video
          v-else-if="zoomMediaData.output_type === 'video'"
          :src=" url + zoomMediaData.file_url"
          class="zoom-media-video"
          controls
      ></video>
    </div>
  </el-dialog>
  <vue-image-mask ref="imageMaskRef" @save-image="handleSaveImage"/>
</template>

<script setup>
import {ref, onMounted,onUnmounted,watch} from 'vue'
import {ElButton, ElInputNumber, ElCarousel, ElCarouselItem, ElImage, ElScrollbar, ElDrawer,ElMessageBox, ElMessage} from 'element-plus'
import {Search, Check,Picture} from '@element-plus/icons-vue'
import VueImageMask from '/@/components/vue-image-mask.vue';
import {useThrottle} from "/@/components/utils.ts";
const currentTab = ref(0)
const imageMaskRef = ref(null);
// 左侧工作流列表数据
const workList = ref([])
const currentData = ref()
// const url = ref(window.location.origin);
const url = ref('https://1005801050051051521-http5006.east2.waas.aigate.cc/');
//const url = ref(import.meta.env.VITE_ADMIN_PROXY_PATH);
const localImages = ref({}) // 存储本地图片的临时URL
// 修改本地图片存储结构，只保存当前图片信息
const currentImage = ref({
  file: null,
  localUrl: null,
  editedUrl: null,
  serverUrl: null,
  item: null
})
// 在setup中添加响应式引用
const inputRefs = ref({})

// 添加输入处理函数
const handleInput = (item, value) => {
  // 在下一个DOM更新周期后重新聚焦
  nextTick(() => {
    if (inputRefs.value[item.key]) {
      // 对于普通input
      if (inputRefs.value[item.key].$el && inputRefs.value[item.key].$el.querySelector('input')) {
        inputRefs.value[item.key].$el.querySelector('input').focus()
      }
      // 对于textarea
      else if (inputRefs.value[item.key].$el && inputRefs.value[item.key].$el.querySelector('textarea')) {
        inputRefs.value[item.key].$el.querySelector('textarea').focus()
      }
      // 直接聚焦（如果组件支持）
      else if (typeof inputRefs.value[item.key].focus === 'function') {
        inputRefs.value[item.key].focus()
      }
    }
  })
}
// 添加触发图片上传的方法
const triggerImageUpload = useThrottle((item) => {
  // 保存当前操作的图片项
  selectedImageItem.value = item
  createFileSelector()
}, 1000);
// 修改 editImage 方法
const editImage = useThrottle((item) => {
  try{
    // 更新当前选中的图片项
    selectedImageItem.value = item

    // 如果有编辑后的图片，使用编辑后的图片
    if (currentImage.value.editedUrl) {
      openImageEditor(currentImage.value.editedUrl)
    }
    // 如果有本地图片，使用本地图片
    else if (currentImage.value.localUrl) {
      openImageEditor(currentImage.value.localUrl)
    }
    // 如果有服务器图片，使用服务器图片
    else if (item.value) {
      // 检查是否为服务器图片
      const isServerImage = item.value.includes('/api/file/download')
      if (isServerImage) {
        currentImage.value.serverUrl = item.value
        currentImage.value.item = item
        openImageEditor(item.value)
      } else {
        // 直接上传新图片
        openImageEditor(item.value)
      }
    } else {
      // 直接上传新图片
      createFileSelector()
    }
  }catch {
    // 直接上传新图片
    createFileSelector()
  }
}, 1000);
// 添加文件输入引用
const fileInputRef = ref(null)
// 创建文件选择器
const createFileSelector = () => {
  const fileInput = document.createElement('input')
  fileInput.type = 'file'
  fileInput.accept = 'image/*'
  fileInput.style.display = 'none'
  fileInput.addEventListener('change', (event) => {
    handleLocalImageSelect(event)
    // 清除input的value，确保下次选择同一文件也能触发change事件
    event.target.value = ''
  })

  document.body.appendChild(fileInput)
  fileInput.click()
  document.body.removeChild(fileInput)
}
// 处理本地图片选择
const handleLocalImageSelect = (event) => {
  const file = event.target.files[0]
  if (file) {
    // 验证文件类型
    const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'image/bmp']
    if (!allowedTypes.includes(file.type)) {
      ElMessage.error('只支持 JPG、PNG、GIF、WebP、BMP 格式的图片')
      return
    }

    // 验证文件大小 (限制为 10MB)
    if (file.size > 10 * 1024 * 1024) {
      ElMessage.error('文件大小不能超过 10MB')
      return
    }

    // 清理之前的本地URL
    if (selectedImageItem.value.localUrl) {
      URL.revokeObjectURL(selectedImageItem.value.localUrl)
    }

    // 重置相关值
    selectedImageItem.value.localUrl = null
    selectedImageItem.value.editedUrl = null
    selectedImageItem.value.serverUrl = null

    // 创建本地URL预览
    const localUrl = URL.createObjectURL(file)

    // 更新当前图片信息
    selectedImageItem.value.file = file
    selectedImageItem.value.localUrl = localUrl

    // 更新表单项的值为本地URL
    selectedImageItem.value.value = localUrl

    // 提示用户可以进行编辑
    ElMessage.success('图片已选择，可以进行编辑')

    // 询问是否需要编辑图片
    ElMessageBox.confirm(
        '是否需要对图片进行编辑？',
        '图片编辑',
        {
          confirmButtonText: '编辑图片',
          cancelButtonText: '跳过编辑'
        }
    )
        .then(() => {
          // 打开图片编辑器
          openImageEditor(localUrl)
        })
        .catch(() => {
          // 用户选择跳过编辑
          ElMessage.info('已跳过图片编辑')
        })
  }
}
// 修改 openImageEditor 方法
const openImageEditor = async (imageUrl) => {
  if (imageMaskRef.value) {
    // 如果是服务器图片，需要先获取图片数据并转换为本地URL格式
    if (selectedImageItem.value.serverUrl && !selectedImageItem.value.localUrl) {
      try {
        // 获取服务器图片数据
        const response = await fetch(imageUrl)
        if (!response.ok) {
          ElMessage.warning('图片加载失败，请选择新图片')
          createFileSelector()
          return
        }
        const blob = await response.blob()

        // 创建本地URL
        const localUrl = URL.createObjectURL(blob)
        selectedImageItem.value.localUrl = localUrl
        selectedImageItem.value.file = new File([blob], 'server_image.jpg', { type: blob.type })

        // 调用VueImageMask组件的方法打开编辑器
        imageMaskRef.value.openMask(localUrl)
      } catch (error) {
        ElMessage.error('图片加载失败')
      }
    } else {
      // 直接使用图片URL
      imageMaskRef.value.openMask(imageUrl)
    }
  } else {
    ElMessage.error('图片编辑器组件未正确加载')
  }
}
// 处理保存编辑后的图片
const handleSaveImage = (editedImageDataUrl) => {
  // 更新编辑后的图片
  selectedImageItem.value.editedUrl = editedImageDataUrl

  // 更新表单项的预览为编辑后的图片
  selectedImageItem.value.value = editedImageDataUrl

  ElMessage.success('图片编辑完成')
  currentEditingImageId.value = null
}
// 添加当前编辑图片ID的引用
const currentEditingImageId = ref(null)
const selectedImageItem = ref(null)
// 切换媒体选择状态
const toggleMediaSelection = (id) => {
  const index = selectedMedia.value.indexOf(id)
  if (index > -1) {
    selectedMedia.value.splice(index, 1)
  } else {
    selectedMedia.value.push(id)
  }

  // 更新 mediaList 中的 selected 状态
  materialFolders.value.forEach(folder => {
    folder.files.forEach(media => {
      if (media.id === id) {
        media.selected = !media.selected
      }
    })
  })
}
// 左侧按钮列表数据
const buttonList = ref([])
// 主内容区域数据
const mainContentData = ref([])
// 媒体列表数据
const mediaList = ref()
// 从接口获取左侧工作流数据
const fetchworkList = () => {
  fetch(url.value + '/api/app/list', {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json'
    },
  }).then(response => {
    if (!response.ok) {
      throw new Error('服务器响应错误');
    }
    return response.json();
  }).then(data => {
    if(data.code === 200){
      workList.value = data.data.list
      currentData.value = data.data.list[0]
      if(currentData.value){
        currentData.value.data = currentData.value.json_detail ? parseJsonData(currentData.value):null;
      }
    } else
      ElMessage.error(data.message);
  }).catch(error => {
    ElMessage.error(error.message);
  })
}
// 检查文件夹中的所有文件是否都被选中
const areAllSelectedInFolder = (folder) => {
  return folder.files.every(file => selectedMedia.value.includes(file.file_id));
};

// 选择文件夹中的所有文件
const selectAllInFolder = (folder) => {
  folder.files.forEach(file => {
    if (!selectedMedia.value.includes(file.file_id)) {
      selectedMedia.value.push(file.file_id);
    }
  });
};

// 取消选择文件夹中的所有文件
const deselectAllInFolder = (folder) => {
  folder.files.forEach(file => {
    const index = selectedMedia.value.indexOf(file.file_id);
    if (index > -1) {
      selectedMedia.value.splice(index, 1);
    }
  });
};
// 数字输入框的值
const num = ref(1)
// 任务抽屉可见性
const drawerVisible = ref(false)
// 生成中任务
const generatingTasks = ref([])
// 排队中任务
const queuedTasks = ref([])
// 已完成任务
const completedTasks = ref([])
// 处理左侧按钮点击事件
const handleButtonClick = useThrottle((item) => {
  mediaList.value = null
  clearInterval(batchDetailTimer.value)
  currentData.value = item
  if(item.json_detail){
    currentData.value.data = parseJsonData(item)
  }else{
    currentData.value.data = null
  }
}, 1000);
// 解析JSON数据
const parseJsonData = (group) => {
  // 初始化结果结构
  const data = [];
  const  dataObj = group['allow_input']
  const keys = Object.keys(dataObj);
  keys.sort((a, b) => {
    return a.localeCompare(b, undefined, { numeric: true, sensitivity: 'base' });
  });
  keys.forEach(key => {
    let type = ''
    let value = dataObj[key]
    if ( typeof value === 'string' ){
      // 当后缀含有常见图片格式时，切换到image类型
      const imageExtensionRegex = /\.(jpg|jpeg|png|bmp|webp|tiff|tif|ico|svg)$/i;
      if (imageExtensionRegex.test(value)) {
        type = 'image';
      }else{
        type = 'string'
      }
    }else if(typeof value === 'number' ){
      type = 'number'
    }else{
      type = 'other'
    }
    let alias = group['allow_input_alias']
    data.push({
      key: key,
      value: type === 'image' ? url.value + '/api/file/input/'+value : value,
      type:type,
      alias: (alias && alias[key])? alias[key] : key.split('.inputs.')[1],
      localUrl: null,
      editedUrl: null,
      serverUrl: type === 'image' && value ? value : null
    });
  });
  return data;
}
// 将Data URL转换为File对象
const dataURLtoFile = (dataurl, filename) => {
  let arr = dataurl.split(','),
      mime = arr[0].match(/:(.*?);/)[1],
      bstr = atob(arr[1]),
      n = bstr.length,
      u8arr = new Uint8Array(n);
  while (n--) {
    u8arr[n] = bstr.charCodeAt(n);
  }
  return new File([u8arr], filename, { type: mime });
}

const runApp = useThrottle(async () => {
  if(!num.value){
    ElMessage.warning('请输入运行次数')
    return
  }
  try {
    // 处理所有图片上传
    const uploadPromises = []

    // 遍历所有数据项
    for (const item of currentData.value.data) {
      if (item.type === 'image') {
        // 检查是否需要上传（有本地图片或编辑后的图片但没有服务器URL）
        if ((item.localUrl || item.editedUrl) && !item.serverUrl) {
          // 优先使用编辑后的图片，如果没有则使用原始本地图片
          const fileToUpload = item.editedUrl ? dataURLtoFile(item.editedUrl, 'edited-image.png') : item.file;

          // 创建上传Promise
          const uploadPromise = uploadImageFile(fileToUpload).then(uploadResult => {
            // 更新服务器URL
            item.serverUrl = uploadResult.serverUrl

            // 更新表单项的值为服务器URL
            const editedOrLocalUrl = item.editedUrl || item.localUrl
            if (item.value === editedOrLocalUrl) {
              item.value = uploadResult.serverUrl
            }

            ElMessage.success('图片上传成功')
          }).catch(error => {
            ElMessage.error('图片上传失败: ' + error.message)
            throw error
          })

          uploadPromises.push(uploadPromise)
        }
      }
    }

    // 等待所有图片上传完成
    await Promise.all(uploadPromises)

    // 更新表单数据中的图片URL
    currentData.value.data.forEach(item => {
      if (item.type === 'image') {
        // 设置到allow_input中
        // 如果有服务器URL，使用服务器URL；否则使用当前值
        const urlToUse = item.serverUrl || item.value
        currentData.value['allow_input'][item.key] = urlToUse.split('/api/file/input/')[1] || item.value.split('/api/file/input/')[1];
      } else {
        currentData.value['allow_input'][item.key] = item.value
      }
    })

    // 提交表单数据
    const response = await fetch(url.value + '/api/batch/create', {
      method: 'post',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        execution_count: num.value,
        allow_input: currentData.value['allow_input'],
        app_id: currentData.value.app_id
      })
    })

    if (!response.ok) {
      throw new Error('服务器响应错误')
    }
    const data = await response.json()
    ElMessage.success('提交成功，开始制作！')
    currentData.value.batch_id = data.data.batch_id
    getBatchDetail()
  } catch (error) {
    ElMessage.error(error.message)
  }
}, 1000);
// 上传单个图片文件到服务器
const uploadImageFile = (file) => {
  return new Promise((resolve, reject) => {
    const formData = new FormData()
    formData.append('file', file)

    fetch(url.value + '/api/file/upload-image', {
      method: 'POST',
      body: formData
    })
        .then(response => {
          if (!response.ok) {
            throw new Error('上传失败')
          }
          return response.json()
        })
        .then(data => {
          if(data.code === 200){
            const serverUrl = url.value  + data.data.file_url
            resolve({
              serverUrl: serverUrl,
              path: data.data.file_url
            })
          } else
            ElMessage.error(data.message);
        })
        .catch(error => {
          reject(error)
        })
  })
}
const getBatchList = ()=>{
  fetch(url.value + '/api/batch/list', {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json'
    },
  }).then(response => {
    if (!response.ok) {
      throw new Error('服务器响应错误');
    }
    return response.json();
  }).then(data => {
    if(data.code === 200){
      // 更清晰的优化版本
      let queued = [], generating = [], completed = [];
      data.data.list.forEach(item => {
        // 根据 status 分发到对应数组
        if (item.status === 0) queued.push(item);
        if (item.status === 1) generating.push(item);
        if (item.status === 2) completed.push(item);
      });
      queuedTasks.value = queued;
      generatingTasks.value = generating;
      completedTasks.value = completed;
    } else
      ElMessage.error(data.message);
  }).catch(error => {
    ElMessage.error(error.message);
  })
}
// 添加用于存储定时器的引用
const batchDetailTimer = ref(null)
const getBatchDetail = ()=>{
  // 清除之前的定时器（如果存在）
  if (batchDetailTimer.value) {
    clearInterval(batchDetailTimer.value)
    batchDetailTimer.value = null
  }

  fetch(url.value + '/api/file/list/' + currentData.value.batch_id, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json'
    },
  }).then(response => {
    if (!response.ok) {
      throw new Error('服务器响应错误');
    }
    return response.json();
  }).then(data => {
    if(data.code === 200){
      mediaList.value = data.data.files
      // 检查 status 是否不为 2，如果不为 2 则每隔 10 秒请求一次
      if (data.data.status !== 2) {
        batchDetailTimer.value = setInterval(() => {
          fetch(url.value + '/api/file/list/' + currentData.value.batch_id, {
            method: 'GET',
            headers: {
              'Content-Type': 'application/json'
            },
          }).then(response => {
            if (!response.ok) {
              throw new Error('服务器响应错误');
            }
            return response.json();
          }).then(data => {
            if(data.code === 200){
              mediaList.value = data.data.files
              // 如果 status 变为 2，清除定时器
              if (data.data.status === 2) {
                clearInterval(batchDetailTimer.value)
                batchDetailTimer.value = null
              }
            } else{
              ElMessage.error('获取详情失败: ' + data.message);
              // 出错时也清除定时器
              clearInterval(batchDetailTimer.value)
              batchDetailTimer.value = null
            }
          }).catch(error => {
            ElMessage.error('获取详情失败: ' + error.message);
            // 出错时也清除定时器
            clearInterval(batchDetailTimer.value)
            batchDetailTimer.value = null
          })
        }, 10000) // 每10秒执行一次
      }
    }else{
      ElMessage.error(data.message);
    }
  }).catch(error => {
    ElMessage.error(error.message);
  })
}
const getALlFileList = ()=>{
  fetch(url.value + '/api/file/list/all', {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json'
    },
  }).then(response => {
    if (!response.ok) {
      throw new Error('服务器响应错误');
    }
    return response.json();
  }).then(data => {
    if(data.code === 200){
      materialFolders.value = data.data.dates
      // 清空选中项
      selectedMedia.value = []
    } else{
      ElMessage.error(data.message);
    }
  }).catch(error => {
    ElMessage.error(error.message);
  })
}
// 打开任务抽屉
const openTaskDrawer = useThrottle(() => {
  drawerVisible.value = true
  getBatchList()
}, 1000);
// 删除排队中的任务
const deleteQueuedTask = useThrottle((id) => {
  queuedTasks.value = queuedTasks.value.filter(task => task.id !== id)
}, 1000);
// 素材库弹窗可见性
const materialDialogVisible = ref(false)
// 素材文件夹数据
const materialFolders = ref()
// 选中的媒体
const selectedMedia = ref([])
// 打开素材库弹窗
const openMaterialDialog = useThrottle(() => {
  currentTab.value = 0
  materialDialogVisible.value = true
  // 这里可以调用接口获取数据
  getALlFileList()
}, 1000);
// 回收站
const moveToTrash = () => {
  currentTab.value = 1
  fetch(url.value + '/api/file/list/recycle', {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json'
    },
  }).then(response => {
    if (!response.ok) {
      throw new Error('服务器响应错误');
    }
    return response.json();
  }).then(data => {
    if(data.code === 200){
      materialFolders.value = data.data.dates
      // 清空选中项
      selectedMedia.value = []
    } else{
      ElMessage.error(data.message);
    }
  }).catch(error => {
    ElMessage.error(error.message);
  })
}
const goBack = () => {
  currentTab.value = 0
  getALlFileList()
}
// 批量下载
const downloadSelected = () => {
  // 检查是否有选中的媒体文件
  if (!selectedMedia.value || selectedMedia.value.length === 0) {
    ElMessage.warning('请先选择要下载的文件');
    return;
  }
  // 实现批量下载的逻辑
  fetch(url.value + '/api/file/batch-download', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      file_ids:selectedMedia.value
    })
  }).then(response => {
      if (!response.ok) {
        throw new Error('下载失败')
      }
      // 从响应头中获取文件名
      const contentDisposition = response.headers.get('Content-Disposition')
      let filename = 'batch-download.zip'
      if (contentDisposition) {
        const filenameMatch = contentDisposition.match(/filename="?([^"]+)"?/)
        if (filenameMatch && filenameMatch[1]) {
          filename = decodeURIComponent(filenameMatch[1])
        }
      }
      // 返回 blob 数据
      return response.blob().then(blob => ({ blob, filename }))
    })
    .then(({ blob, filename }) => {
      // 创建下载链接
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = filename
      document.body.appendChild(link)
      link.click()
      // 清理DOM和URL对象
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)
      ElMessage.success('下载成功')
    })
    .catch(error => {
      ElMessage.error('下载失败: ' + error.message)
    })
}
// 删除选中
const deleteSelected = () => {
  // 检查是否有选中的媒体文件
  if (!selectedMedia.value || selectedMedia.value.length === 0) {
    ElMessage.warning('请先选择要删除的文件');
    return;
  }
  // 实现删除的逻辑
  fetch(url.value + '/api/file/batch-delete', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      file_ids: selectedMedia.value
    })
  })
      .then(response => {
        if (!response.ok) {
          throw new Error('删除失败')
        }
        return response.json()
      })
      .then(data => {
        if (data.code === 200) {
          ElMessage.success(data.message)
          materialFolders.value.forEach(folder => {
            folder.files = folder.files.filter(file =>
                !selectedMedia.value.includes(file.file_id)
            )
          })
          // 过滤掉空的文件夹
          materialFolders.value = materialFolders.value.filter(folder =>
              folder.files && folder.files.length > 0
          )
          // 清空选中项
          selectedMedia.value = []
        } else {
          ElMessage.error(data.message || '删除失败')
        }
      })
      .catch(error => {
        ElMessage.error(error.message)
      })
}
// 鼠标悬停的媒体ID
const hoveredMediaId = ref(null)
// 显示放大图标
const showZoomIcon = (id) => {
  hoveredMediaId.value = id
}
// 隐藏放大图标
const hideZoomIcon = (id) => {
  if (hoveredMediaId.value === id) {
    hoveredMediaId.value = null
  }
}
// 放大媒体对话框可见性
const zoomMediaDialogVisible = ref(false)
// 放大媒体数据
const zoomMediaData = ref({})
// 放大媒体
const zoomMedia = (media) => {
  zoomMediaData.value = media;
  zoomMediaDialogVisible.value = true;
}
// 组件挂载时获取左侧按钮数据
onMounted(() => {
  fetchworkList()
  getBatchList()
})
// 定时器引用
const batchListTimer = ref(null)
// 监听 generatingTasks 和 queuedTasks 的长度变化
watch([generatingTasks, queuedTasks], ([newGenerating, newQueued]) => {
  // 如果任一数组长度不为0且定时器不存在，则启动定时器
  if ((newGenerating.length > 0 || newQueued.length > 0) && !batchListTimer.value) {
    batchListTimer.value = setInterval(() => {
      getBatchList()
    }, 10000) // 10秒执行一次
  }
  // 如果两个数组都为空且定时器存在，则清除定时器
  else if (newGenerating.length === 0 && newQueued.length === 0 && batchListTimer.value) {
    clearInterval(batchListTimer.value)
    batchListTimer.value = null
  }
}, { deep: true })

// 在组件卸载时清理定时器
onUnmounted(() => {
  // 清理所有图片项的本地URL
  if (currentData.value && currentData.value.data) {
    currentData.value.data.forEach(item => {
      if (item.type === 'image' && item.localUrl) {
        URL.revokeObjectURL(item.localUrl)
      }
    })
  }

  if (batchListTimer.value) {
    clearInterval(batchListTimer.value)
    batchListTimer.value = null
  }
  if (batchDetailTimer.value) {
    clearInterval(batchDetailTimer.value)
    batchDetailTimer.value = null
  }

})
</script>

<style>
.folder-section h3 {
  margin-bottom: 10px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-right: 5px;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

body {
  padding: 0;
  margin: 0;
  height: 100vh;
  overflow: hidden;
  background-color: #f5f5f5; /* 页面背景色改为灰色 */
}
.content-item {
  margin-bottom: 20px;
  padding: 15px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  background-color: #fff;
}

.content-item h3 {
  margin-bottom: 10px;
  color: #333;
  font-size: 16px;
}

.input-wrapper {
  margin-top: 10px;
}

.full-width {
  width: 100% !important;
}

.image-upload-container {
  display: flex;
  flex-direction: column;
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  padding: 15px;
  transition: border-color 0.3s;
  min-height: 200px;
}

.image-preview-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-grow: 1;
  min-height: 150px;
  margin-bottom: 10px;
}

.image-upload-container:hover {
  border-color: #409eff;
}

.image-preview {
  max-width: 100%;
  height: 150px;
  margin-bottom: 10px;
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #999;
  flex-grow: 1;
  min-height: 150px;
}

.upload-placeholder .el-icon {
  font-size: 32px;
  margin-bottom: 10px;
}

.image-actions {
  display: flex;
  gap: 10px;
  justify-content: center;
  padding: 10px 0;
}

.upload-button, .edit-button {
  flex: 1;
}
.zoom-icon {
  position: absolute;
  bottom: 5px;
  right: 5px;
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  width: 30px;
  height: 30px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  color: white;
}

.select-checkbox {
  position: absolute;
  top: 5px;
  right: 5px;
}

.full-container {
  height: 100vh;
  background-color: #f5f5f5; /* 容器背景色也改为灰色 */
}

.el-container {
  height: 100%;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  color: white;
  border-bottom: 1px solid #ddd; /* 顶部添加边框 */
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  z-index: 10;
}

.logo-container {
  display: flex;
  align-items: center;
  width: 200px;
  justify-content: center;
  img{
    width: 112px;
  }
}

.logo {
  height: 40px;
  width: auto;
}

.header-title {
  flex: 1;
  font-size: 20px;
  font-weight: 600;
  text-align: center;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  color: #002758;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  letter-spacing: 1px;
}

.button-container {
  display: flex;
  gap: 10px;
}

.left-button, .right-button {
  margin: 10px;
}

/* 左侧边栏样式 */
.left-aside {
  background-color: #f5f5f5;
  padding: 20px 0;
  overflow: hidden;
  height: calc(100vh - 60px);
}

.button-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.sidebar-button-first {
  margin: 0 10px;
}

.sidebar-button {
  margin: 0 10px;
  padding: 0 10px;
  > span {
    flex: 1;
    width: 190px;
    justify-content: center;
  }
}

/* 添加文本省略样式 */
.button-text-ellipsis {
  display: inline-block;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  vertical-align: middle;
}
.sidebar-button-active {
  background-color: #409eff !important;
  color: white !important;
}

.sidebar-button-active .button-text {
  color: white !important;
}

.sidebar-button-active .edit-icon {
  color: white !important;
}

/* 主内容区域样式 */
.main-content-wrapper {
  display: flex;
  flex-direction: column;
  position: relative;
  margin:0 20px;
  flex: 2;
  overflow: hidden;
  padding: 20px 0 50px  0 !important;
}

.content-item {
  margin-bottom: 20px;
  padding: 15px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  background-color: #fff;
}

.main-footer {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding: 15px 20px;
  background-color: #fff;
  border-radius: 4px;
  position: sticky;
  bottom: 15px;
  z-index: 10;
}

.run-section {
  display: flex;
  align-items: center;
  gap: 12px;
}

.run-button {
  padding: 8px 20px;
  font-size: 14px;
  font-weight: 500;
  width: 160px;
}

.times-input {
  display: flex;
  align-items: center;
  gap: 6px;
  /* 调整输入框样式 */
  .el-input-number {
    width: 100px;
  }
}

.times-label {
  font-size: 14px;
  color: #606266;
  white-space: nowrap;
}

/* 右侧边栏样式 */
.right-aside {
  background-color: #f5f5f5;
  padding: 20px;
  display: flex;
  flex-direction: column;
  flex: 3;
}

.info-section {
  margin-bottom: 20px;
  padding: 15px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  background-color: #fff;
}

.media-section {
  flex: 1;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding-bottom: 50px;
}

.el-carousel {
  flex: 1;
  height: 100% !important;
}

.el-carousel__container {
  height: 100% !important;
}

.media-item {
  width: 100%;
  height: 100%;
}

.task-section {
  margin-bottom: 30px;
}

.task-section h3 {
  margin-bottom: 15px;
  color: #333;
}

.task-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
  border-bottom: 1px solid #eee;
}

.queued-task {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.queued-task-right {
  display: flex;
  align-items: center;
  gap: 15px;
}

.task-name {
  font-size: 14px;
  color: #666;
}

.task-count {
  font-size: 14px;
  color: #999;
}

.delete-button {
  padding: 5px 10px;
}

.no-tasks {
  color: #999;
  font-size: 14px;
  text-align: center;
  padding: 10px 0;
}

/* 素材库弹窗样式 */
.material-dialog-content {
  height: 60vh;
  overflow-y: auto;
}

.folder-section {
  margin-bottom: 20px;
}

.folder-section h3 {
  margin-bottom: 10px;
}

.media-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 10px;
}

.media-item {
  position: relative;
  cursor: pointer;
  border: 2px solid transparent;
  border-radius: 4px;
  overflow: hidden;
}

.media-item.selected {
  border-color: #409eff;
}

.media-thumbnail {
  width: 100%;
  height: 150px;
  object-fit: cover;
}

.media-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
}

.selected-icon {
  font-size: 24px;
  color: white;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

/* 放大媒体对话框样式 */
.zoom-media-dialog-content {
  min-height: 400px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.zoom-media-image {
  max-width: 100%;
  max-height: 100%;
  img{
    max-height: 80vh;
  }
}

.zoom-media-video {
  max-width: 100%;
  max-height: 100%;
  video{
    max-height: 80vh;
  }
}
.el-carousel__button{
  height: 4px !important;
  width: 10px !important;
  background-color: #409eff !important;
}
</style>