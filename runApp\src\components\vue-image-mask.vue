<template>
  <el-dialog
      v-model="isMaskVisible"
      :title="'给图片添加蒙版'"
      width="70%"
      top="5vh"
      :modal="true"
      :show-close="true"
      @close="closeMask"
      class="image-mask-dialog"
      :append-to-body="true"
      :close-on-click-modal="false"
  >
    <div
        class="canvas-container"
        :class="{ 'brush-mode': currentMode === 'brush' }"
        :style="{
          '--brush-size': brushSize + 'px',
          '--cursor-x': cursorX + 'px',
          '--cursor-y': cursorY + 'px'
        }"
        @mousemove="updateCursorPosition"
    >
      <canvas
          ref="canvas"
          :width="canvasWidth"
          :height="canvasHeight"
          :class="['edit-canvas', currentMode === 'brush' ? 'brush-mode' : 'drag-mode']"
          @mousedown="handleMouseDown"
          @mousemove="handleMouseMove"
          @mouseup="handleMouseUp"
          @mouseleave="handleMouseLeave"
          @touchstart.prevent="handleTouchStart"
          @touchmove.prevent="handleTouchMove"
          @touchend.prevent="handleTouchEnd"
          @wheel.prevent="handleZoom"
      ></canvas>
    </div>
    <div class="bottom-controls">
      <span>笔刷大小：</span>
      <el-slider
          v-model="brushSize"
          :min="10"
          :max="100"
          style="width: 200px"
      />
      <el-button type="primary" @click="resetCanvas">重置</el-button>
      <el-button type="primary" @click="undoLastDraw">撤回</el-button>
      <el-button :type="currentMode === 'brush' ? 'primary' : 'info'" @click="currentMode = 'brush'">笔刷</el-button>
      <el-button :type="currentMode === 'drag' ? 'primary' : 'info'" @click="currentMode = 'drag'">拖动</el-button>
      <el-button type="success" @click="saveAndExit">保存</el-button>
    </div>
  </el-dialog>
</template>


<script setup>
import { ref, nextTick } from 'vue';
// 响应式状态
const brushSize = ref(20);
const canvasWidth = ref(0);
const canvasHeight = ref(0);
const isDrawing = ref(false);
const originalImage = ref(null);
const canvas = ref(null);
// 新增状态
const isMaskVisible = ref(false);
const scale = ref(1);
const offsetX = ref(0);
const offsetY = ref(0);
const cursorX = ref(0);
const cursorY = ref(0);
const isDragging = ref(false);
const lastX = ref(0);
const lastY = ref(0);
// 添加记录鼠标路径的变量
const prevX = ref(null);
const prevY = ref(null);
// 模式切换状态 - 'brush' 或 'drag'
const currentMode = ref('brush');
// 添加涂鸦历史记录数组
const drawingHistory = ref([]);
// 声明自定义事件
const emit = defineEmits(['save-image']);

// 修改openMask方法，移除Promise相关代码
const openMask = (imageSrc) => {
  const img = new Image();
  img.onload = () => {
    originalImage.value = img;
    canvasWidth.value = img.width;
    canvasHeight.value = img.height;
    isMaskVisible.value = true;
    // 删除document.addEventListener('keydown', handleEsc);
    nextTick(() => resetCanvas());
  };
  img.src = imageSrc;
};

// 统一事件处理函数
const handleMouseDown = (e) => {
  if (currentMode.value === 'brush') {
    startDrawing(e);
  } else if (currentMode.value === 'drag') {
    startDrag(e);
  }
};

const handleMouseMove = (e) => {
  if (currentMode.value === 'brush') {
    draw(e);
  } else if (currentMode.value === 'drag' && isDragging.value) {
    handleDrag(e);
  }
};

const handleMouseUp = () => {
  if (currentMode.value === 'brush') {
    stopDrawing();
  } else if (currentMode.value === 'drag') {
    stopDrag();
  }
};

const handleMouseLeave = () => {
  handleMouseUp();
};

// 触摸事件处理
const handleTouchStart = (e) => {
  if (currentMode.value === 'brush') {
    startDrawing(e);
  } else if (currentMode.value === 'drag') {
    startDrag(e);
  }
};

const handleTouchMove = (e) => {
  if (currentMode.value === 'brush') {
    draw(e);
  } else if (currentMode.value === 'drag' && isDragging.value) {
    handleDrag(e);
  }
};

const handleTouchEnd = () => {
  handleMouseUp();
};

const startDrawing = (e) => {
  isDrawing.value = true;
  const ctx = canvas.value?.getContext('2d');
  if (!ctx) return;

  const rect = canvas.value.getBoundingClientRect();
  const clientX = e.type.includes('touch') ? e.touches[0].clientX : e.clientX;
  const clientY = e.type.includes('touch') ? e.touches[0].clientY : e.clientY;

  const mouseX = clientX - rect.left;
  const mouseY = clientY - rect.top;
  const x = mouseX / scale.value;
  const y = mouseY / scale.value;

  prevX.value = x;
  prevY.value = y;

  ctx.beginPath();
  ctx.moveTo(x, y);
};

function draw(e) {
  if (!isDrawing.value) return;
  const ctx = canvas.value?.getContext('2d');
  if (!ctx) return;

  // 添加抗锯齿设置
  ctx.imageSmoothingEnabled = true;
  ctx.imageSmoothingQuality = 'high';
  ctx.lineCap = 'round';
  ctx.lineJoin = 'round';
  ctx.miterLimit = 1;

  ctx.globalCompositeOperation = 'destination-out';
  ctx.strokeStyle = 'rgba(255,255,255,1)';
  ctx.lineWidth = brushSize.value / scale.value;

  const rect = canvas.value.getBoundingClientRect();
  const clientX = e.type.includes('touch') ? e.touches[0].clientX : e.clientX;
  const clientY = e.type.includes('touch') ? e.touches[0].clientY : e.clientY;
  // 坐标优化：添加0.5像素偏移并取整，解决Canvas亚像素渲染问题
  const mouseX = clientX - rect.left;
  const mouseY = clientY - rect.top;
  const x = Math.round(mouseX / scale.value) + 0.5;
  const y = Math.round(mouseY / scale.value) + 0.5;

  // 使用贝塞尔曲线平滑路径
  if (prevX.value !== null && prevY.value !== null) {
    const cpX = (prevX.value + x) / 2;
    const cpY = (prevY.value + y) / 2;
    ctx.quadraticCurveTo(prevX.value, prevY.value, cpX, cpY);
    ctx.stroke();
    ctx.beginPath();
    ctx.moveTo(cpX, cpY);
  } else {
    ctx.beginPath();
    ctx.moveTo(x, y);
  }

  prevX.value = x;
  prevY.value = y;
}

const resetCanvas = () => {
  const ctx = canvas.value?.getContext('2d');
  if (!ctx || !originalImage.value) return;

  // 保存当前上下文状态
  ctx.save();

  ctx.imageSmoothingEnabled = true;
  ctx.imageSmoothingQuality = 'high';
  // 重置合成模式为默认（避免受涂鸦模式影响）
  ctx.globalCompositeOperation = 'source-over';
  // 清除整个画布
  ctx.clearRect(0, 0, canvasWidth.value, canvasHeight.value);
  // 重新绘制原始图像
  ctx.drawImage(originalImage.value, 0, 0, canvasWidth.value, canvasHeight.value);
  // 恢复上下文状态
  ctx.restore();

  // 重置缩放和平移
  scale.value = 1;
  offsetX.value = 0;
  offsetY.value = 0;
  updateCanvasTransform();

  // 重置历史记录并保存初始状态
  const initialState = ctx.getImageData(0, 0, canvasWidth.value, canvasHeight.value);
  drawingHistory.value = [initialState];
};

// 添加撤回上一步操作的方法
const undoLastDraw = () => {
  if (drawingHistory.value.length <= 1) return; // 至少保留初始状态

  // 移除最后一次绘制记录
  drawingHistory.value.pop();

  // 恢复上一次的画布状态
  const ctx = canvas.value?.getContext('2d');
  if (ctx) {
    ctx.putImageData(drawingHistory.value[drawingHistory.value.length - 1], 0, 0);
  }
};

// 修改停止绘制方法，添加历史记录保存
const stopDrawing = () => {
  if (!isDrawing.value) return; // 添加状态检查，避免重复保存
  isDrawing.value = false;

  // 保存当前绘制状态到历史记录
  const ctx = canvas.value?.getContext('2d');
  if (ctx) {
    const currentState = ctx.getImageData(0, 0, canvasWidth.value, canvasHeight.value);
    drawingHistory.value.push(currentState);
  }
};


// 关闭涂鸦遮罩层
const closeMask = () => {
  isMaskVisible.value = false;
};
// 处理缩放
const handleZoom = (e) => {
  const canvasEl = canvas.value;
  if (!canvasEl) return;

  const rect = canvasEl.getBoundingClientRect();
  const mouseX = e.clientX - rect.left;
  const mouseY = e.clientY - rect.top;

  // 检查鼠标是否在画布上
  const isMouseOnCanvas = mouseX >= 0 && mouseX <= rect.width && mouseY >= 0 && mouseY <= rect.height;

  const delta = e.deltaY > 0 ? -0.1 : 0.1;
  const newScale = Math.max(0.5, Math.min(scale.value + delta, 3));

  if (newScale !== scale.value) {
    if (isMouseOnCanvas) {
      // 鼠标在画布上，以鼠标位置为原点缩放
      const beforeX = (mouseX - offsetX.value) / scale.value;
      const beforeY = (mouseY - offsetY.value) / scale.value;

      scale.value = newScale;

      offsetX.value = mouseX - beforeX * scale.value;
      offsetY.value = mouseY - beforeY * scale.value;
    } else {
      // 鼠标不在画布上，以画布中心为原点缩放
      const centerX = rect.width / 2;
      const centerY = rect.height / 2;
      const beforeX = (centerX - offsetX.value) / scale.value;
      const beforeY = (centerY - offsetY.value) / scale.value;

      scale.value = newScale;

      offsetX.value = centerX - beforeX * scale.value;
      offsetY.value = centerY - beforeY * scale.value;
    }

    updateCanvasTransform();
  }
};

// 更新画布变换
const updateCanvasTransform = () => {
  const canvasEl = canvas.value;
  if (canvasEl) {
    canvasEl.style.transform = `scale(${scale.value}) translate(${offsetX.value / scale.value}px, ${offsetY.value / scale.value}px)`;
    canvasEl.style.transformOrigin = '0 0';
  }
};

// 拖拽相关方法
const startDrag = (e) => {
  isDragging.value = true;
  lastX.value = e.type.includes('touch') ? e.touches[0].clientX : e.clientX;
  lastY.value = e.type.includes('touch') ? e.touches[0].clientY : e.clientY;
};

const handleDrag = (e) => {
  if (!isDragging.value) return;
  const clientX = e.type.includes('touch') ? e.touches[0].clientX : e.clientX;
  const clientY = e.type.includes('touch') ? e.touches[0].clientY : e.clientY;
  // 修复：拖动偏移量需除以当前缩放比例
  const deltaX = (clientX - lastX.value) / scale.value;
  const deltaY = (clientY - lastY.value) / scale.value;

  offsetX.value += deltaX;
  offsetY.value += deltaY;

  lastX.value = clientX;
  lastY.value = clientY;

  updateCanvasTransform();
};

const stopDrag = () => {
  isDragging.value = false;
};

// 保存并退出
const saveAndExit = () => {
  const dataUrl = canvas.value.toDataURL('image/png');
  // 触发自定义事件向父组件传递图片数据
  emit('save-image', dataUrl);
  closeMask();
};
const updateCursorPosition = (e) => {
  const rect = e.currentTarget.getBoundingClientRect(); // 修改为currentTarget
  cursorX.value = e.clientX - rect.left;
  cursorY.value = e.clientY - rect.top;
};
// 暴露openMask方法供外部调用
defineExpose({ openMask });
</script>

<style scoped>
/* 修复画布容器样式 */
.canvas-container {
  border: 1px solid #ddd;
  border-radius: 4px;
  overflow: hidden;
  background-color: #f9f9f9;
  flex-grow: 1; /* 使用flex-grow替代固定高度 */
  display: flex;
  height: 70vh;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.bottom-controls {
  margin-top: 15px;
  display: flex;
  gap: 10px;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.5);
  padding: 10px 20px;
  border-radius: 4px;
  color: white;
  flex-wrap: wrap;
  justify-content: center;
}
.edit-canvas {
  max-width: 100%;
  height: auto;
  transition: transform 0.1s ease;
}
/* 模式相关样式 */
.brush-mode {
  cursor: none;
  position: relative;
}

.brush-mode::after {
  content: '';
  position: absolute;
  width: var(--brush-size);
  height: var(--brush-size);
  border: 2px solid rgba(0,0,0,0.5);
  border-radius: 50%;
  pointer-events: none;
  transform: translate(-50%, -50%);
  left: var(--cursor-x, 0);
  top: var(--cursor-y, 0);
  z-index: 1; /* 添加此属性 */
}
.drag-mode {
  cursor: grab;
}
.drag-mode:active {
  cursor: grabbing;
}
/* 响应式调整 */
@media (max-width: 768px) {
  .bottom-controls {
    gap: 5px;
    padding: 5px 10px;
  }
  .bottom-controls .el-button {
    padding: 6px 12px;
    font-size: 12px;
  }
}
</style>
