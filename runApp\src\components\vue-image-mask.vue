<template>
  <el-dialog
      v-model="dialogVisible"
      title="给图片添加蒙版"
      width="80%"
      top="5vh"
      :modal="true"
      :show-close="true"
      @close="handleClose"
      class="image-mask-dialog"
      :append-to-body="true"
      :close-on-click-modal="false"
  >
    <div class="mask-editor">


      <!-- 画布容器 -->
      <div
        ref="canvasContainer"
        class="canvas-container"
        :class="containerClasses"
        @wheel.prevent="handleWheel"
        @mousedown="handleContainerMouseDown"
        @mousemove="handleContainerMouseMove"
        @mouseup="handleContainerMouseUp"
        @mouseleave="handleContainerMouseLeave"
        @touchstart.prevent="handleContainerTouchStart"
        @touchmove.prevent="handleContainerTouchMove"
        @touchend.prevent="handleContainerTouchEnd"
      >
        <canvas
          ref="mainCanvas"
          class="main-canvas"
          :style="canvasStyle"
        ></canvas>

        <!-- 自定义光标 -->
        <div
          v-if="editorMode === 'brush' && showCursor"
          class="brush-cursor"
          :style="cursorStyle"
        ></div>
      </div>

      <!-- 状态信息 -->
      <div class="status-bar">
        <span>缩放: {{ Math.round(viewport.scale * 100) }}%</span>
        <!-- 工具栏 -->
        <div class="toolbar">
          <div class="tool-group">
            <span class="tool-label">模式：</span>
            <el-button-group>
              <el-button
                  :type="editorMode === 'brush' ? 'primary' : 'default'"
                  @click="setMode('brush')"
                  size="small"
              >
                笔刷
              </el-button>
              <el-button
                  :type="editorMode === 'pan' ? 'primary' : 'default'"
                  @click="setMode('pan')"
                  size="small"
              >
                拖拽
              </el-button>
            </el-button-group>
          </div>

          <div class="tool-group" v-show="editorMode === 'brush'">
            <span class="tool-label">笔刷大小：</span>
            <el-slider
                v-model="brushSize"
                :min="5"
                :max="100"
                :step="1"
                style="width: 150px; margin: 0 10px;"
            />
            <span class="brush-size-value">{{ brushSize }}px</span>
          </div>

          <div class="tool-group">
            <el-button @click="resetImage" size="small">重置</el-button>
            <el-button @click="undoAction" :disabled="!canUndo" size="small">撤销</el-button>
            <el-button @click="redoAction" :disabled="!canRedo" size="small">重做</el-button>
          </div>

          <div class="tool-group">
            <el-button type="success" @click="saveImage" size="small">保存</el-button>
          </div>
        </div>
        <span>位置: ({{ Math.round(viewport.x) }}, {{ Math.round(viewport.y) }})</span>
      </div>
    </div>
  </el-dialog>
</template>


<script setup>
import { ref, computed, nextTick, onMounted, onUnmounted } from 'vue'

// 组件事件
const emit = defineEmits(['save-image'])

// 核心状态
const dialogVisible = ref(false)
const editorMode = ref('brush') // 'brush' | 'pan'
const brushSize = ref(20)

// Canvas 引用
const canvasContainer = ref(null)
const mainCanvas = ref(null)

// 图像数据
const originalImageData = ref(null)
const currentImageData = ref(null)

// 视口状态 (用于缩放和平移)
const viewport = ref({
  x: 0,
  y: 0,
  scale: 1
})

// 交互状态
const isInteracting = ref(false)
const lastPointer = ref({ x: 0, y: 0 })
const currentPointer = ref({ x: 0, y: 0 })
const showCursor = ref(false)

// 历史记录 (撤销/重做)
const history = ref([])
const historyIndex = ref(-1)
const maxHistorySize = 50

// 计算属性
const canUndo = computed(() => historyIndex.value > 0)
const canRedo = computed(() => historyIndex.value < history.value.length - 1)

const containerClasses = computed(() => ({
  'brush-mode': editorMode.value === 'brush',
  'pan-mode': editorMode.value === 'pan',
  'interacting': isInteracting.value
}))

const canvasStyle = computed(() => ({
  transform: `translate(${viewport.value.x}px, ${viewport.value.y}px) scale(${viewport.value.scale})`,
  transformOrigin: '0 0'
}))

const cursorStyle = computed(() => ({
  left: `${currentPointer.value.x}px`,
  top: `${currentPointer.value.y}px`,
  width: `${brushSize.value * viewport.value.scale}px`,
  height: `${brushSize.value * viewport.value.scale}px`
}))

// 核心方法
const openMask = async (imageSrc) => {
  try {
    const img = await loadImage(imageSrc)
    await initializeCanvas(img)
    dialogVisible.value = true
  } catch (error) {
    console.error('Failed to load image:', error)
  }
}

const loadImage = (src) => {
  return new Promise((resolve, reject) => {
    const img = new Image()
    img.onload = () => resolve(img)
    img.onerror = reject
    img.src = src
  })
}

const initializeCanvas = async (img) => {
  await nextTick()

  const canvas = mainCanvas.value
  if (!canvas) return

  // 设置画布尺寸
  canvas.width = img.width
  canvas.height = img.height

  const ctx = canvas.getContext('2d')
  ctx.imageSmoothingEnabled = true
  ctx.imageSmoothingQuality = 'high'

  // 绘制原始图像
  ctx.drawImage(img, 0, 0)

  // 保存原始图像数据
  originalImageData.value = ctx.getImageData(0, 0, img.width, img.height)
  currentImageData.value = ctx.getImageData(0, 0, img.width, img.height)

  // 重置视口
  resetViewport()

  // 初始化历史记录
  saveToHistory()
}

const resetViewport = () => {
  const canvas = mainCanvas.value
  const container = canvasContainer.value
  if (!canvas || !container) return

  const containerRect = container.getBoundingClientRect()
  const canvasAspect = canvas.width / canvas.height
  const containerAspect = containerRect.width / containerRect.height

  let scale = 1
  if (canvasAspect > containerAspect) {
    scale = (containerRect.width * 0.8) / canvas.width
  } else {
    scale = (containerRect.height * 0.8) / canvas.height
  }

  viewport.value = {
    x: (containerRect.width - canvas.width * scale) / 2,
    y: (containerRect.height - canvas.height * scale) / 2,
    scale: Math.max(0.1, Math.min(scale, 5))
  }
}

// 工具方法
const setMode = (mode) => {
  editorMode.value = mode
  showCursor.value = false
}

const getPointerPosition = (e) => {
  const container = canvasContainer.value
  if (!container) return { x: 0, y: 0 }

  const rect = container.getBoundingClientRect()
  const clientX = e.touches ? e.touches[0].clientX : e.clientX
  const clientY = e.touches ? e.touches[0].clientY : e.clientY

  return {
    x: clientX - rect.left,
    y: clientY - rect.top
  }
}

const getCanvasPosition = (containerPos) => {
  return {
    x: (containerPos.x - viewport.value.x) / viewport.value.scale,
    y: (containerPos.y - viewport.value.y) / viewport.value.scale
  }
}

// 事件处理
const handleContainerMouseDown = (e) => {
  e.preventDefault()
  const pos = getPointerPosition(e)
  startInteraction(pos)
}

const handleContainerMouseMove = (e) => {
  const pos = getPointerPosition(e)
  updatePointer(pos)
  if (isInteracting.value) {
    continueInteraction(pos)
  }
}

const handleContainerMouseUp = () => {
  endInteraction()
}

const handleContainerMouseLeave = () => {
  showCursor.value = false
  endInteraction()
}

// 触摸事件
const handleContainerTouchStart = (e) => {
  const pos = getPointerPosition(e)
  startInteraction(pos)
}

const handleContainerTouchMove = (e) => {
  const pos = getPointerPosition(e)
  updatePointer(pos)
  if (isInteracting.value) {
    continueInteraction(pos)
  }
}

const handleContainerTouchEnd = () => {
  endInteraction()
}

// 交互逻辑
const startInteraction = (pos) => {
  isInteracting.value = true
  lastPointer.value = { ...pos }

  if (editorMode.value === 'brush') {
    startBrushStroke(pos)
  }
}

const continueInteraction = (pos) => {
  if (editorMode.value === 'brush') {
    continueBrushStroke(pos)
  } else if (editorMode.value === 'pan') {
    panViewport(pos)
  }

  lastPointer.value = { ...pos }
}

const endInteraction = () => {
  if (isInteracting.value && editorMode.value === 'brush') {
    endBrushStroke()
  }
  isInteracting.value = false
}

const updatePointer = (pos) => {
  currentPointer.value = { ...pos }
  showCursor.value = editorMode.value === 'brush'
}

// 笔刷功能
const startBrushStroke = (pos) => {
  const canvasPos = getCanvasPosition(pos)
  const canvas = mainCanvas.value
  if (!canvas) return

  const ctx = canvas.getContext('2d')
  ctx.save()
  ctx.globalCompositeOperation = 'destination-out'
  ctx.lineCap = 'round'
  ctx.lineJoin = 'round'
  ctx.lineWidth = brushSize.value
  ctx.beginPath()
  ctx.moveTo(canvasPos.x, canvasPos.y)
}

const continueBrushStroke = (pos) => {
  const canvasPos = getCanvasPosition(pos)
  const canvas = mainCanvas.value
  if (!canvas) return

  const ctx = canvas.getContext('2d')
  ctx.lineTo(canvasPos.x, canvasPos.y)
  ctx.stroke()
}

const endBrushStroke = () => {
  const canvas = mainCanvas.value
  if (!canvas) return

  const ctx = canvas.getContext('2d')
  ctx.restore()

  // 保存当前状态到历史记录
  saveToHistory()
}

// 平移视口
const panViewport = (pos) => {
  const deltaX = pos.x - lastPointer.value.x
  const deltaY = pos.y - lastPointer.value.y

  viewport.value.x += deltaX
  viewport.value.y += deltaY
}

// 缩放功能
const handleWheel = (e) => {
  const pos = getPointerPosition(e)
  const canvasPos = getCanvasPosition(pos)

  const scaleFactor = e.deltaY > 0 ? 0.9 : 1.1
  const newScale = Math.max(0.1, Math.min(viewport.value.scale * scaleFactor, 5))

  if (newScale !== viewport.value.scale) {
    // 以鼠标位置为中心缩放
    viewport.value.x = pos.x - canvasPos.x * newScale
    viewport.value.y = pos.y - canvasPos.y * newScale
    viewport.value.scale = newScale
  }
}

// 历史记录管理
const saveToHistory = () => {
  const canvas = mainCanvas.value
  if (!canvas) return

  const ctx = canvas.getContext('2d')
  const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height)

  // 移除当前索引之后的历史记录
  history.value = history.value.slice(0, historyIndex.value + 1)

  // 添加新的历史记录
  history.value.push(imageData)

  // 限制历史记录大小
  if (history.value.length > maxHistorySize) {
    history.value.shift()
  } else {
    historyIndex.value++
  }
}

const undoAction = () => {
  if (!canUndo.value) return

  historyIndex.value--
  restoreFromHistory()
}

const redoAction = () => {
  if (!canRedo.value) return

  historyIndex.value++
  restoreFromHistory()
}

const restoreFromHistory = () => {
  const canvas = mainCanvas.value
  if (!canvas || historyIndex.value < 0 || historyIndex.value >= history.value.length) return

  const ctx = canvas.getContext('2d')
  ctx.putImageData(history.value[historyIndex.value], 0, 0)
}
// 重置图像
const resetImage = () => {
  const canvas = mainCanvas.value
  if (!canvas || !originalImageData.value) return

  const ctx = canvas.getContext('2d')
  ctx.putImageData(originalImageData.value, 0, 0)

  // 重置视口
  resetViewport()

  // 重置历史记录
  history.value = []
  historyIndex.value = -1
  saveToHistory()
}

// 保存图像
const saveImage = () => {
  const canvas = mainCanvas.value
  if (!canvas) return

  const dataUrl = canvas.toDataURL('image/png')
  emit('save-image', dataUrl)
  handleClose()
}

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false

  // 清理状态
  isInteracting.value = false
  showCursor.value = false
  editorMode.value = 'brush'

  // 清理数据
  originalImageData.value = null
  currentImageData.value = null
  history.value = []
  historyIndex.value = -1
}

// 生命周期
onMounted(() => {
  // 可以在这里添加键盘事件监听等
})

onUnmounted(() => {
  // 清理事件监听器
})

// 暴露方法给父组件
defineExpose({
  openMask
})
</script>

<style scoped>
.image-mask-dialog {
  --primary-color: #409eff;
  --border-color: #dcdfe6;
  --bg-color: #f5f7fa;
}

.mask-editor {
  display: flex;
  flex-direction: column;
  height: 75vh;
  gap: 16px;
}

.toolbar {
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 12px 16px;
  background: var(--bg-color);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  flex-wrap: wrap;
}

.tool-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.tool-label {
  font-size: 14px;
  color: #606266;
  white-space: nowrap;
}

.brush-size-value {
  font-size: 12px;
  color: #909399;
  min-width: 35px;
}

.canvas-container {
  flex: 1;
  position: relative;
  border: 2px solid var(--border-color);
  border-radius: 8px;
  background: #ffffff;
  overflow: hidden;
  user-select: none;
}

.canvas-container.brush-mode {
  cursor: none;
}

.canvas-container.pan-mode {
  cursor: grab;
}

.canvas-container.pan-mode.interacting {
  cursor: grabbing;
}

.main-canvas {
  display: block;
  transition: transform 0.1s ease-out;
}

.brush-cursor {
  position: absolute;
  border: 2px solid var(--primary-color);
  border-radius: 50%;
  pointer-events: none;
  transform: translate(-50%, -50%);
  z-index: 10;
  opacity: 0.8;
}

.status-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 16px;
  background: var(--bg-color);
  border: 1px solid var(--border-color);
  border-radius: 6px;
  font-size: 12px;
  color: #909399;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .mask-editor {
    height: 70vh;
  }

  .toolbar {
    gap: 12px;
    padding: 8px 12px;
  }

  .tool-group {
    gap: 6px;
  }

  .tool-label {
    font-size: 12px;
  }

  .status-bar {
    flex-direction: column;
    gap: 4px;
    align-items: flex-start;
  }
}

@media (max-width: 480px) {
  .toolbar {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }

  .tool-group {
    justify-content: center;
  }
}
</style>
