<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图片遮罩组件测试</title>
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://unpkg.com/element-plus/dist/index.full.js"></script>
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .test-images {
            display: flex;
            gap: 20px;
            flex-wrap: wrap;
            margin-bottom: 20px;
        }
        .test-image {
            border: 2px solid #ddd;
            border-radius: 8px;
            cursor: pointer;
            transition: border-color 0.3s;
        }
        .test-image:hover {
            border-color: #409eff;
        }
        .test-image img {
            display: block;
            max-width: 200px;
            max-height: 150px;
            object-fit: cover;
        }
        .instructions {
            background: #f0f9ff;
            border: 1px solid #0ea5e9;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .instructions h3 {
            margin-top: 0;
            color: #0369a1;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="test-container">
            <h1>图片遮罩组件测试</h1>
            
            <div class="instructions">
                <h3>测试说明：</h3>
                <ul>
                    <li>点击下方任意图片打开遮罩编辑器</li>
                    <li>测试不同比例的图片（特别是宽度很长的图片）</li>
                    <li>验证画笔位置是否与鼠标位置一致</li>
                    <li>测试缩放和拖拽功能</li>
                </ul>
            </div>

            <div class="test-images">
                <!-- 正常比例图片 -->
                <div class="test-image" @click="openMask('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjMwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjNDI4NWY0Ii8+CiAgPHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCwgc2Fucy1zZXJpZiIgZm9udC1zaXplPSIyNCIgZmlsbD0id2hpdGUiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGR5PSIuM2VtIj7mraPluLjmr5Lkvos8L3RleHQ+Cjwvc3ZnPgo=')">
                    <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjMwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjNDI4NWY0Ii8+CiAgPHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCwgc2Fucy1zZXJpZiIgZm9udC1zaXplPSIyNCIgZmlsbD0id2hpdGUiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGR5PSIuM2VtIj7mraPluLjmr5Lkvos8L3RleHQ+Cjwvc3ZnPgo=" alt="正常比例">
                </div>

                <!-- 宽图片 -->
                <div class="test-image" @click="openMask('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZWY0NDQ0Ii8+CiAgPHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCwgc2Fucy1zZXJpZiIgZm9udC1zaXplPSIyNCIgZmlsbD0id2hpdGUiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGR5PSIuM2VtIj7lrr3lm77niYc8L3RleHQ+Cjwvc3ZnPgo=')">
                    <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZWY0NDQ0Ii8+CiAgPHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCwgc2Fucy1zZXJpZiIgZm9udC1zaXplPSIyNCIgZmlsbD0id2hpdGUiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGR5PSIuM2VtIj7lrr3lm77niYc8L3RleHQ+Cjwvc3ZnPgo=" alt="宽图片">
                </div>

                <!-- 超宽图片 -->
                <div class="test-image" @click="openMask('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIwMCIgaGVpZ2h0PSIxNTAiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CiAgPHJlY3Qgd2lkdGg9IjEwMCUiIGhlaWdodD0iMTAwJSIgZmlsbD0iIzEwYjk4MSIvPgogIDx0ZXh0IHg9IjUwJSIgeT0iNTAlIiBmb250LWZhbWlseT0iQXJpYWwsIHNhbnMtc2VyaWYiIGZvbnQtc2l6ZT0iMjQiIGZpbGw9IndoaXRlIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBkeT0iLjNlbSI+6LaF5a695Zu+54mHPC90ZXh0Pgo8L3N2Zz4K')">
                    <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIwMCIgaGVpZ2h0PSIxNTAiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CiAgPHJlY3Qgd2lkdGg9IjEwMCUiIGhlaWdodD0iMTAwJSIgZmlsbD0iIzEwYjk4MSIvPgogIDx0ZXh0IHg9IjUwJSIgeT0iNTAlIiBmb250LWZhbWlseT0iQXJpYWwsIHNhbnMtc2VyaWYiIGZvbnQtc2l6ZT0iMjQiIGZpbGw9IndoaXRlIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBkeT0iLjNlbSI+6LaF5a695Zu+54mHPC90ZXh0Pgo8L3N2Zz4K" alt="超宽图片">
                </div>

                <!-- 高图片 -->
                <div class="test-image" @click="openMask('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjYwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjOGI1Y2Y2Ii8+CiAgPHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCwgc2Fucy1zZXJpZiIgZm9udC1zaXplPSIyNCIgZmlsbD0id2hpdGUiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGR5PSIuM2VtIj7pq5jlm77niYc8L3RleHQ+Cjwvc3ZnPgo=')">
                    <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjYwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjOGI1Y2Y2Ii8+CiAgPHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCwgc2Fucy1zZXJpZiIgZm9udC1zaXplPSIyNCIgZmlsbD0id2hpdGUiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGR5PSIuM2VtIj7pq5jlm77niYc8L3RleHQ+Cjwvc3ZnPgo=" alt="高图片">
                </div>
            </div>

            <!-- 图片遮罩组件 -->
            <vue-image-mask ref="imageMaskRef" @save-image="handleSaveImage"></vue-image-mask>
        </div>
    </div>

    <script>
        const { createApp, ref } = Vue;
        const { ElMessage } = ElementPlus;

        // 完整的图片遮罩组件（包含修复后的逻辑）
        const VueImageMask = {
            template: `
                <el-dialog
                    v-model="isMaskVisible"
                    title="给图片添加蒙版"
                    width="70%"
                    top="5vh"
                    :modal="true"
                    :show-close="true"
                    @close="closeMask"
                    class="image-mask-dialog"
                    :append-to-body="true"
                    :close-on-click-modal="false"
                >
                    <div
                        ref="canvasContainer"
                        class="canvas-container"
                        :class="{ 'brush-mode': currentMode === 'brush' }"
                        :style="{
                            '--brush-size': brushSize * scale + 'px',
                            '--cursor-x': cursorX + 'px',
                            '--cursor-y': cursorY + 'px'
                        }"
                        @mousemove="updateCursorPosition"
                    >
                        <canvas
                            ref="canvas"
                            :width="canvasWidth"
                            :height="canvasHeight"
                            :class="['edit-canvas', currentMode === 'brush' ? 'brush-mode' : 'drag-mode']"
                            @mousedown="handleMouseDown"
                            @mousemove="handleMouseMove"
                            @mouseup="handleMouseUp"
                            @mouseleave="handleMouseLeave"
                            @wheel.prevent="handleZoom"
                        ></canvas>
                    </div>
                    <div class="bottom-controls">
                        <span>笔刷大小：</span>
                        <el-slider
                            v-model="brushSize"
                            :min="10"
                            :max="100"
                            style="width: 200px"
                        />
                        <el-button type="primary" @click="resetCanvas">重置</el-button>
                        <el-button :type="currentMode === 'brush' ? 'primary' : 'info'" @click="currentMode = 'brush'">笔刷</el-button>
                        <el-button :type="currentMode === 'drag' ? 'primary' : 'info'" @click="currentMode = 'drag'">拖动</el-button>
                        <el-button type="success" @click="saveAndExit">保存</el-button>
                    </div>
                </el-dialog>
            `,
            emits: ['save-image'],
            setup(props, { emit, expose }) {
                const { nextTick } = Vue;

                // 响应式状态
                const brushSize = ref(20);
                const canvasWidth = ref(0);
                const canvasHeight = ref(0);
                const isDrawing = ref(false);
                const originalImage = ref(null);
                const canvas = ref(null);
                const canvasContainer = ref(null);
                const isMaskVisible = ref(false);
                const scale = ref(1);
                const offsetX = ref(0);
                const offsetY = ref(0);
                const cursorX = ref(0);
                const cursorY = ref(0);
                const isDragging = ref(false);
                const lastX = ref(0);
                const lastY = ref(0);
                const prevX = ref(null);
                const prevY = ref(null);
                const currentMode = ref('brush');

                // 修复后的坐标转换函数
                const screenToCanvas = (clientX, clientY) => {
                    const canvasEl = canvas.value;
                    if (!canvasEl) return { x: 0, y: 0 };

                    const rect = canvasEl.getBoundingClientRect();
                    const mouseX = clientX - rect.left;
                    const mouseY = clientY - rect.top;

                    // 计算CSS缩放比例（画布显示尺寸 vs 实际尺寸）
                    const cssScaleX = rect.width / canvasWidth.value;
                    const cssScaleY = rect.height / canvasHeight.value;

                    // 逆向变换计算：
                    // 变换顺序是 translate(offsetX, offsetY) scale(scale)
                    // 所以逆向是：先除以scale，再减去offset

                    // 1. 先转换为画布显示坐标系
                    const displayX = mouseX / cssScaleX;
                    const displayY = mouseY / cssScaleY;

                    // 2. 逆向用户变换：先除以缩放，再减去平移
                    const finalX = displayX / scale.value - offsetX.value / scale.value;
                    const finalY = displayY / scale.value - offsetY.value / scale.value;

                    return { x: finalX, y: finalY };
                };

                const startDrawing = (e) => {
                    isDrawing.value = true;
                    const ctx = canvas.value?.getContext('2d');
                    if (!ctx) return;

                    const clientX = e.type.includes('touch') ? e.touches[0].clientX : e.clientX;
                    const clientY = e.type.includes('touch') ? e.touches[0].clientY : e.clientY;

                    const { x, y } = screenToCanvas(clientX, clientY);

                    prevX.value = x;
                    prevY.value = y;

                    ctx.beginPath();
                    ctx.moveTo(x, y);
                };

                const draw = (e) => {
                    if (!isDrawing.value) return;
                    const ctx = canvas.value?.getContext('2d');
                    if (!ctx) return;

                    ctx.imageSmoothingEnabled = true;
                    ctx.imageSmoothingQuality = 'high';
                    ctx.lineCap = 'round';
                    ctx.lineJoin = 'round';
                    ctx.miterLimit = 1;
                    ctx.globalCompositeOperation = 'destination-out';
                    ctx.strokeStyle = 'rgba(255,255,255,1)';
                    ctx.lineWidth = brushSize.value;

                    const clientX = e.type.includes('touch') ? e.touches[0].clientX : e.clientX;
                    const clientY = e.type.includes('touch') ? e.touches[0].clientY : e.clientY;

                    const { x, y } = screenToCanvas(clientX, clientY);

                    if (prevX.value !== null && prevY.value !== null) {
                        const cpX = (prevX.value + x) / 2;
                        const cpY = (prevY.value + y) / 2;
                        ctx.quadraticCurveTo(prevX.value, prevY.value, cpX, cpY);
                        ctx.stroke();
                        ctx.beginPath();
                        ctx.moveTo(cpX, cpY);
                    } else {
                        ctx.beginPath();
                        ctx.moveTo(x, y);
                    }

                    prevX.value = x;
                    prevY.value = y;
                };

                const stopDrawing = () => {
                    isDrawing.value = false;
                    prevX.value = null;
                    prevY.value = null;
                };

                const openMask = (imageSrc) => {
                    const img = new Image();
                    img.onload = () => {
                        originalImage.value = img;
                        canvasWidth.value = img.width;
                        canvasHeight.value = img.height;
                        isMaskVisible.value = true;
                        nextTick(() => resetCanvas());
                    };
                    img.src = imageSrc;
                };

                const resetCanvas = () => {
                    const ctx = canvas.value?.getContext('2d');
                    if (!ctx || !originalImage.value) return;

                    ctx.save();
                    ctx.imageSmoothingEnabled = true;
                    ctx.imageSmoothingQuality = 'high';
                    ctx.globalCompositeOperation = 'source-over';
                    ctx.clearRect(0, 0, canvasWidth.value, canvasHeight.value);
                    ctx.drawImage(originalImage.value, 0, 0, canvasWidth.value, canvasHeight.value);
                    ctx.restore();

                    scale.value = 1;
                    offsetX.value = 0;
                    offsetY.value = 0;
                    updateCanvasTransform();
                };

                const updateCanvasTransform = () => {
                    const canvasEl = canvas.value;
                    if (canvasEl) {
                        // 应用变换：先平移，再缩放
                        canvasEl.style.transform = \`translate(\${offsetX.value}px, \${offsetY.value}px) scale(\${scale.value})\`;
                        canvasEl.style.transformOrigin = '0 0';
                    }
                };

                const updateCursorPosition = (e) => {
                    const containerRect = canvasContainer.value?.getBoundingClientRect();
                    if (!containerRect) return;

                    cursorX.value = e.clientX - containerRect.left;
                    cursorY.value = e.clientY - containerRect.top;
                };

                const handleZoom = (e) => {
                    const canvasEl = canvas.value;
                    if (!canvasEl) return;

                    const rect = canvasEl.getBoundingClientRect();
                    const mouseX = e.clientX - rect.left;
                    const mouseY = e.clientY - rect.top;

                    const delta = e.deltaY > 0 ? -0.1 : 0.1;
                    const newScale = Math.max(0.5, Math.min(scale.value + delta, 3));

                    if (newScale !== scale.value) {
                        const cssScaleX = rect.width / canvasWidth.value;
                        const cssScaleY = rect.height / canvasHeight.value;

                        const displayX = mouseX / cssScaleX;
                        const displayY = mouseY / cssScaleY;

                        const beforeX = displayX / scale.value - offsetX.value / scale.value;
                        const beforeY = displayY / scale.value - offsetY.value / scale.value;

                        scale.value = newScale;

                        offsetX.value = displayX - beforeX * scale.value;
                        offsetY.value = displayY - beforeY * scale.value;

                        updateCanvasTransform();
                    }
                };

                const handleMouseDown = (e) => {
                    if (currentMode.value === 'brush') {
                        startDrawing(e);
                    }
                };

                const handleMouseMove = (e) => {
                    if (currentMode.value === 'brush') {
                        draw(e);
                    }
                };

                const handleMouseUp = () => {
                    if (currentMode.value === 'brush') {
                        stopDrawing();
                    }
                };

                const handleMouseLeave = () => {
                    handleMouseUp();
                };

                const closeMask = () => {
                    isMaskVisible.value = false;
                };

                const saveAndExit = () => {
                    const dataUrl = canvas.value.toDataURL('image/png');
                    emit('save-image', dataUrl);
                    closeMask();
                };

                expose({ openMask });

                return {
                    isMaskVisible,
                    canvasContainer,
                    canvas,
                    canvasWidth,
                    canvasHeight,
                    brushSize,
                    currentMode,
                    scale,
                    cursorX,
                    cursorY,
                    closeMask,
                    updateCursorPosition,
                    handleMouseDown,
                    handleMouseMove,
                    handleMouseUp,
                    handleMouseLeave,
                    handleZoom,
                    resetCanvas,
                    saveAndExit
                };
            }
        };

        const app = createApp({
            components: {
                VueImageMask
            },
            setup() {
                const imageMaskRef = ref(null);

                const openMask = (imageSrc) => {
                    if (imageMaskRef.value) {
                        imageMaskRef.value.openMask(imageSrc);
                    }
                };

                const handleSaveImage = (dataUrl) => {
                    ElMessage.success('图片已保存');
                    console.log('保存的图片数据:', dataUrl);
                };

                return {
                    imageMaskRef,
                    openMask,
                    handleSaveImage
                };
            }
        });

        app.use(ElementPlus);
        app.mount('#app');
    </script>

    <style>
        .canvas-container {
            border: 1px solid #ddd;
            border-radius: 4px;
            overflow: hidden;
            background-color: #f9f9f9;
            display: flex;
            height: 70vh;
            justify-content: center;
            align-items: center;
            position: relative;
        }

        .edit-canvas {
            max-width: 100%;
            height: auto;
            transition: transform 0.1s ease;
        }

        .brush-mode {
            cursor: none;
        }

        .brush-mode::after {
            content: '';
            position: absolute;
            width: var(--brush-size);
            height: var(--brush-size);
            border: 2px solid rgba(0,0,0,0.5);
            border-radius: 50%;
            pointer-events: none;
            transform: translate(-50%, -50%);
            left: var(--cursor-x, 0);
            top: var(--cursor-y, 0);
            z-index: 1;
        }

        .drag-mode {
            cursor: grab;
        }

        .drag-mode:active {
            cursor: grabbing;
        }

        .bottom-controls {
            margin-top: 15px;
            display: flex;
            gap: 10px;
            align-items: center;
            background-color: rgba(0, 0, 0, 0.5);
            padding: 10px 20px;
            border-radius: 4px;
            color: white;
            flex-wrap: wrap;
            justify-content: center;
        }
    </style>
</body>
</html>
