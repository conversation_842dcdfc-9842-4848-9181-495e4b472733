{"name": "<PERSON><PERSON><PERSON>", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build", "preview": "vite preview"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "element-plus": "^2.10.1", "vue": "^3.5.17"}, "devDependencies": {"@types/node": "^24.1.0", "@types/vue": "^2.0.0", "@vitejs/plugin-vue": "^6.0.0", "@vue/tsconfig": "^0.7.0", "typescript": "~5.8.3", "unplugin-auto-import": "^0.13.0", "vite": "^4.3.3", "vite-plugin-compression": "^0.5.1", "vite-plugin-style-import": "^2.0.0", "vite-plugin-top-level-await": "^1.3.0", "vite-plugin-vue-devtools": "^7.1.3", "vite-plugin-vue-setup-extend": "^0.4.0", "vue-eslint-parser": "^9.1.0", "vue-tsc": "^2.2.12"}}