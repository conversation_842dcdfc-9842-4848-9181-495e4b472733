<template>
  <el-dialog
      v-model="dialogVisible"
      title="给图片添加蒙版"
      width="80%"
      top="5vh"
      :modal="true"
      :show-close="true"
      @close="handleClose"
      class="image-mask-dialog"
      :append-to-body="true"
      :close-on-click-modal="false"
  >
    <div class="mask-editor">
      <!-- 工具栏 -->
      <div class="toolbar">
        <div class="tool-group">
          <span class="tool-label">模式：</span>
          <el-button-group>
            <el-button
              :type="editorMode === 'brush' ? 'primary' : 'default'"
              @click="setMode('brush')"
              size="small"
            >
              笔刷
            </el-button>
            <el-button
              :type="editorMode === 'pan' ? 'primary' : 'default'"
              @click="setMode('pan')"
              size="small"
            >
              拖拽
            </el-button>
          </el-button-group>
        </div>

        <div class="tool-group" v-show="editorMode === 'brush'">
          <span class="tool-label">笔刷大小：</span>
          <el-slider
            v-model="brushSize"
            :min="5"
            :max="100"
            :step="1"
            style="width: 150px; margin: 0 10px;"
          />
          <span class="brush-size-value">{{ brushSize }}px</span>
        </div>

        <div class="tool-group">
          <el-button @click="resetImage" size="small">重置</el-button>
          <el-button @click="undoAction" :disabled="!canUndo" size="small">撤销</el-button>
          <el-button @click="redoAction" :disabled="!canRedo" size="small">重做</el-button>
        </div>

        <div class="tool-group">
          <el-button type="success" @click="saveImage" size="small">保存</el-button>
        </div>
      </div>

      <!-- 画布容器 -->
      <div
        ref="canvasContainer"
        class="canvas-container"
        :class="containerClasses"
        @wheel.prevent="handleWheel"
        @mousedown="handleContainerMouseDown"
        @mousemove="handleContainerMouseMove"
        @mouseup="handleContainerMouseUp"
        @mouseleave="handleContainerMouseLeave"
        @touchstart.prevent="handleContainerTouchStart"
        @touchmove.prevent="handleContainerTouchMove"
        @touchend.prevent="handleContainerTouchEnd"
      >
        <canvas
          ref="mainCanvas"
          class="main-canvas"
          :style="canvasStyle"
        ></canvas>

        <!-- 自定义光标 -->
        <div
          v-if="editorMode === 'brush' && showCursor"
          class="brush-cursor"
          :style="cursorStyle"
        ></div>
      </div>

      <!-- 状态信息 -->
      <div class="status-bar">
        <span>缩放: {{ Math.round(viewport.scale * 100) }}%</span>
        <span>位置: ({{ Math.round(viewport.x) }}, {{ Math.round(viewport.y) }})</span>
      </div>
    </div>
  </el-dialog>
</template>


<script setup>
import { ref, computed, nextTick, onMounted, onUnmounted } from 'vue'

// 组件事件
const emit = defineEmits(['save-image'])

// 核心状态
const dialogVisible = ref(false)
const editorMode = ref('brush') // 'brush' | 'pan'
const brushSize = ref(20)

// Canvas 引用
const canvasContainer = ref(null)
const mainCanvas = ref(null)

// 图像数据
const originalImageData = ref(null)
const currentImageData = ref(null)

// 视口状态 (用于缩放和平移)
const viewport = ref({
  x: 0,
  y: 0,
  scale: 1
})

// 交互状态
const isInteracting = ref(false)
const lastPointer = ref({ x: 0, y: 0 })
const currentPointer = ref({ x: 0, y: 0 })
const showCursor = ref(false)

// 历史记录 (撤销/重做)
const history = ref([])
const historyIndex = ref(-1)
const maxHistorySize = 50

// 计算属性
const canUndo = computed(() => historyIndex.value > 0)
const canRedo = computed(() => historyIndex.value < history.value.length - 1)

const containerClasses = computed(() => ({
  'brush-mode': editorMode.value === 'brush',
  'pan-mode': editorMode.value === 'pan',
  'interacting': isInteracting.value
}))

const canvasStyle = computed(() => ({
  transform: `translate(${viewport.value.x}px, ${viewport.value.y}px) scale(${viewport.value.scale})`,
  transformOrigin: '0 0'
}))

const cursorStyle = computed(() => ({
  left: `${currentPointer.value.x}px`,
  top: `${currentPointer.value.y}px`,
  width: `${brushSize.value * viewport.value.scale}px`,
  height: `${brushSize.value * viewport.value.scale}px`
}))

// 核心方法
const openMask = async (imageSrc) => {
  try {
    const img = await loadImage(imageSrc)
    await initializeCanvas(img)
    dialogVisible.value = true
  } catch (error) {
    console.error('Failed to load image:', error)
  }
}

const loadImage = (src) => {
  return new Promise((resolve, reject) => {
    const img = new Image()
    img.onload = () => resolve(img)
    img.onerror = reject
    img.src = src
  })
}

const initializeCanvas = async (img) => {
  await nextTick()

  const canvas = mainCanvas.value
  if (!canvas) return

  // 设置画布尺寸
  canvas.width = img.width
  canvas.height = img.height

  const ctx = canvas.getContext('2d')
  ctx.imageSmoothingEnabled = true
  ctx.imageSmoothingQuality = 'high'

  // 绘制原始图像
  ctx.drawImage(img, 0, 0)

  // 保存原始图像数据
  originalImageData.value = ctx.getImageData(0, 0, img.width, img.height)
  currentImageData.value = ctx.getImageData(0, 0, img.width, img.height)

  // 重置视口
  resetViewport()

  // 初始化历史记录
  saveToHistory()
}

const resetViewport = () => {
  const canvas = mainCanvas.value
  const container = canvasContainer.value
  if (!canvas || !container) return

  const containerRect = container.getBoundingClientRect()
  const canvasAspect = canvas.width / canvas.height
  const containerAspect = containerRect.width / containerRect.height

  let scale = 1
  if (canvasAspect > containerAspect) {
    scale = (containerRect.width * 0.8) / canvas.width
  } else {
    scale = (containerRect.height * 0.8) / canvas.height
  }

  viewport.value = {
    x: (containerRect.width - canvas.width * scale) / 2,
    y: (containerRect.height - canvas.height * scale) / 2,
    scale: Math.max(0.1, Math.min(scale, 5))
  }
}

// 工具方法
const setMode = (mode) => {
  editorMode.value = mode
  showCursor.value = false
}

const getPointerPosition = (e) => {
  const container = canvasContainer.value
  if (!container) return { x: 0, y: 0 }

  const rect = container.getBoundingClientRect()
  const clientX = e.touches ? e.touches[0].clientX : e.clientX
  const clientY = e.touches ? e.touches[0].clientY : e.clientY

  return {
    x: clientX - rect.left,
    y: clientY - rect.top
  }
}

const getCanvasPosition = (containerPos) => {
  return {
    x: (containerPos.x - viewport.value.x) / viewport.value.scale,
    y: (containerPos.y - viewport.value.y) / viewport.value.scale
  }
}

// 事件处理
const handleContainerMouseDown = (e) => {
  e.preventDefault()
  const pos = getPointerPosition(e)
  startInteraction(pos)
}

const handleContainerMouseMove = (e) => {
  const pos = getPointerPosition(e)
  updatePointer(pos)
  if (isInteracting.value) {
    continueInteraction(pos)
  }
}

const handleContainerMouseUp = () => {
  endInteraction()
}

const handleContainerMouseLeave = () => {
  showCursor.value = false
  endInteraction()
}

// 触摸事件
const handleContainerTouchStart = (e) => {
  const pos = getPointerPosition(e)
  startInteraction(pos)
}

const handleContainerTouchMove = (e) => {
  const pos = getPointerPosition(e)
  updatePointer(pos)
  if (isInteracting.value) {
    continueInteraction(pos)
  }
}

const handleContainerTouchEnd = () => {
  endInteraction()
}

// 交互逻辑
const startInteraction = (pos) => {
  isInteracting.value = true
  lastPointer.value = { ...pos }

  if (editorMode.value === 'brush') {
    startBrushStroke(pos)
  }
}

const continueInteraction = (pos) => {
  if (editorMode.value === 'brush') {
    continueBrushStroke(pos)
  } else if (editorMode.value === 'pan') {
    panViewport(pos)
  }

  lastPointer.value = { ...pos }
}

const endInteraction = () => {
  if (isInteracting.value && editorMode.value === 'brush') {
    endBrushStroke()
  }
  isInteracting.value = false
}

const updatePointer = (pos) => {
  currentPointer.value = { ...pos }
  showCursor.value = editorMode.value === 'brush'
}

// 笔刷功能
const startBrushStroke = (pos) => {
  const canvasPos = getCanvasPosition(pos)
  const canvas = mainCanvas.value
  if (!canvas) return

  const ctx = canvas.getContext('2d')
  ctx.save()
  ctx.globalCompositeOperation = 'destination-out'
  ctx.lineCap = 'round'
  ctx.lineJoin = 'round'
  ctx.lineWidth = brushSize.value
  ctx.beginPath()
  ctx.moveTo(canvasPos.x, canvasPos.y)
}

const continueBrushStroke = (pos) => {
  const canvasPos = getCanvasPosition(pos)
  const canvas = mainCanvas.value
  if (!canvas) return

  const ctx = canvas.getContext('2d')
  ctx.lineTo(canvasPos.x, canvasPos.y)
  ctx.stroke()
}

const endBrushStroke = () => {
  const canvas = mainCanvas.value
  if (!canvas) return

  const ctx = canvas.getContext('2d')
  ctx.restore()

  // 保存当前状态到历史记录
  saveToHistory()
}

// 添加撤回上一步操作的方法
const undoLastDraw = () => {
  if (drawingHistory.value.length <= 1) return; // 至少保留初始状态

  // 移除最后一次绘制记录
  drawingHistory.value.pop();

  // 恢复上一次的画布状态
  const ctx = canvas.value?.getContext('2d');
  if (ctx) {
    ctx.putImageData(drawingHistory.value[drawingHistory.value.length - 1], 0, 0);
  }
};

// 修改停止绘制方法，添加历史记录保存
const stopDrawing = () => {
  if (!isDrawing.value) return; // 添加状态检查，避免重复保存
  isDrawing.value = false;

  // 保存当前绘制状态到历史记录
  const ctx = canvas.value?.getContext('2d');
  if (ctx) {
    const currentState = ctx.getImageData(0, 0, canvasWidth.value, canvasHeight.value);
    drawingHistory.value.push(currentState);
  }
};


// 关闭涂鸦遮罩层
const closeMask = () => {
  isMaskVisible.value = false;
};
// 处理缩放
const handleZoom = (e) => {
  const canvasEl = canvas.value;
  if (!canvasEl) return;

  const rect = canvasEl.getBoundingClientRect();
  const mouseX = e.clientX - rect.left;
  const mouseY = e.clientY - rect.top;

  // 检查鼠标是否在画布上
  const isMouseOnCanvas = mouseX >= 0 && mouseX <= rect.width && mouseY >= 0 && mouseY <= rect.height;

  const delta = e.deltaY > 0 ? -0.1 : 0.1;
  const newScale = Math.max(0.5, Math.min(scale.value + delta, 3));

  if (newScale !== scale.value) {
    if (isMouseOnCanvas) {
      // 鼠标在画布上，以鼠标位置为原点缩放
      const beforeX = (mouseX - offsetX.value) / scale.value;
      const beforeY = (mouseY - offsetY.value) / scale.value;

      scale.value = newScale;

      offsetX.value = mouseX - beforeX * scale.value;
      offsetY.value = mouseY - beforeY * scale.value;
    } else {
      // 鼠标不在画布上，以画布中心为原点缩放
      const centerX = rect.width / 2;
      const centerY = rect.height / 2;
      const beforeX = (centerX - offsetX.value) / scale.value;
      const beforeY = (centerY - offsetY.value) / scale.value;

      scale.value = newScale;

      offsetX.value = centerX - beforeX * scale.value;
      offsetY.value = centerY - beforeY * scale.value;
    }

    updateCanvasTransform();
  }
};

// 更新画布变换
const updateCanvasTransform = () => {
  const canvasEl = canvas.value;
  if (canvasEl) {
    canvasEl.style.transform = `scale(${scale.value}) translate(${offsetX.value / scale.value}px, ${offsetY.value / scale.value}px)`;
    canvasEl.style.transformOrigin = '0 0';
  }
};

// 拖拽相关方法
const startDrag = (e) => {
  isDragging.value = true;
  lastX.value = e.type.includes('touch') ? e.touches[0].clientX : e.clientX;
  lastY.value = e.type.includes('touch') ? e.touches[0].clientY : e.clientY;
};

const handleDrag = (e) => {
  if (!isDragging.value) return;
  const clientX = e.type.includes('touch') ? e.touches[0].clientX : e.clientX;
  const clientY = e.type.includes('touch') ? e.touches[0].clientY : e.clientY;
  // 修复：拖动偏移量需除以当前缩放比例
  const deltaX = (clientX - lastX.value) / scale.value;
  const deltaY = (clientY - lastY.value) / scale.value;

  offsetX.value += deltaX;
  offsetY.value += deltaY;

  lastX.value = clientX;
  lastY.value = clientY;

  updateCanvasTransform();
};

const stopDrag = () => {
  isDragging.value = false;
};

// 保存并退出
const saveAndExit = () => {
  const dataUrl = canvas.value.toDataURL('image/png');
  // 触发自定义事件向父组件传递图片数据
  emit('save-image', dataUrl);
  closeMask();
};
const updateCursorPosition = (e) => {
  const rect = e.currentTarget.getBoundingClientRect(); // 修改为currentTarget
  cursorX.value = e.clientX - rect.left;
  cursorY.value = e.clientY - rect.top;
};
// 暴露openMask方法供外部调用
defineExpose({ openMask });
</script>

<style scoped>
/* 修复画布容器样式 */
.canvas-container {
  border: 1px solid #ddd;
  border-radius: 4px;
  overflow: hidden;
  background-color: #f9f9f9;
  flex-grow: 1; /* 使用flex-grow替代固定高度 */
  display: flex;
  height: 70vh;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.bottom-controls {
  margin-top: 15px;
  display: flex;
  gap: 10px;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.5);
  padding: 10px 20px;
  border-radius: 4px;
  color: white;
  flex-wrap: wrap;
  justify-content: center;
}
.edit-canvas {
  max-width: 100%;
  height: auto;
  transition: transform 0.1s ease;
}
/* 模式相关样式 */
.brush-mode {
  cursor: none;
  position: relative;
}

.brush-mode::after {
  content: '';
  position: absolute;
  width: var(--brush-size);
  height: var(--brush-size);
  border: 2px solid rgba(0,0,0,0.5);
  border-radius: 50%;
  pointer-events: none;
  transform: translate(-50%, -50%);
  left: var(--cursor-x, 0);
  top: var(--cursor-y, 0);
  z-index: 1; /* 添加此属性 */
}
.drag-mode {
  cursor: grab;
}
.drag-mode:active {
  cursor: grabbing;
}
/* 响应式调整 */
@media (max-width: 768px) {
  .bottom-controls {
    gap: 5px;
    padding: 5px 10px;
  }
  .bottom-controls .el-button {
    padding: 6px 12px;
    font-size: 12px;
  }
}
</style>
