import { createApp } from 'vue'
import './style.css'
import App from './App.vue'
import ElementPlus from 'element-plus';
import 'element-plus/dist/index.css'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
const app = createApp(App)
app.use(ElementPlus);

// 在创建Vue应用后立即注册图标组件
const { EditPen, Picture, Document, Upload, ArrowDown, Download,QuestionFilled,Loading ,Edit} = ElementPlusIconsVue;

// 显式注册每个图标组件（修复大小写问题）
app.component('edit-pen', EditPen);
app.component('a-picture', Picture);
app.component('document', Document);
app.component('Upload', Upload);
app.component('arrow-down', ArrowDown);
app.component('download', Download);
app.component('question-filled', QuestionFilled);
app.component('loading', Loading);
app.component('edit', Edit);

app.mount('#app');