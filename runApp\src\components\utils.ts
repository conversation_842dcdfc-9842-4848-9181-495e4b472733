/**
 * 节流函数
 * @param fn - 需要节流的函数
 * @param delay - 节流的时间间隔，单位毫秒
 * @param options - 配置选项
 * @param options.leading - 是否在开始时立即执行一次，默认true
 * @param options.trailing - 是否在结束后再执行一次，默认true
 * @returns 节流后的函数
 */
export const useThrottle = <T extends (...args: any[]) => any>(
    fn: T,
    delay: number,
    { leading = true, trailing = true } = {}
): T & { cancel: () => void } => {
    let lastTime = 0;
    let timer: ReturnType<typeof setTimeout> | null = null;

    const throttled = function(this: any, ...args: Parameters<T>): void {
        const now = Date.now();

        // 如果是第一次执行且不允许leading执行，则设置lastTime为当前时间
        if (!lastTime && !leading) {
            lastTime = now;
        }

        // 计算剩余时间
        const remaining = delay - (now - lastTime);

        // 如果剩余时间小于等于0，说明可以执行了
        if (remaining <= 0) {
            // 清除可能存在的定时器
            if (timer) {
                clearTimeout(timer);
                timer = null;
            }
            // 执行函数
            fn.apply(this, args);
            // 更新最后执行时间
            lastTime = now;
        }
        // 如果需要 trailing 执行且当前没有定时器
        else if (trailing && !timer) {
            // 设置定时器，在剩余时间后执行一次
            timer = setTimeout(() => {
                fn.apply(this, args);
                // 执行完后重置lastTime和timer
                lastTime = leading ? Date.now() : 0;
                timer = null;
            }, remaining);
        }
    } as T & { cancel: () => void };

    // 取消节流函数的方法
    throttled.cancel = function(): void {
        if (timer) {
            clearTimeout(timer);
            timer = null;
        }
        lastTime = 0;
    };

    return throttled;
};
