<template>
  <el-container class="full-container">
    <el-header class="header">
      <div class="logo-container">
        <img src="/@/assets/logo.png" alt="Logo" class="logo">
      </div>
      <div class="header-title">制作工作流应用</div>
    </el-header>
    <el-container>
      <!-- 左侧边栏：竖列工作流 -->
      <el-aside width="240px" class="left-aside">
        <el-scrollbar class="left-aside-scrollbar">
          <div class="button-list">
            <el-button
                class="sidebar-button-first"
                @click="addComfyApp()"
            >
              新增Comfy应用
            </el-button>
            <el-button
                v-for="(item,index) in workList"
                :key="item.id"
                class="sidebar-button"
                :class="{ 'sidebar-button-active': currentData && currentData.app_id === item.app_id }"
                @click="handleButtonClick(item,index)"
            >
              <div class="button-content">
                <div class="text-container">
                  <span class="button-text">{{ item.app_name }}</span>
                </div>
                <el-icon class="edit-icon" @click.stop="editItemName(item)">
                  <Edit/>
                </el-icon>
              </div>
            </el-button>
          </div>
        </el-scrollbar>
      </el-aside>
      <template v-if="!currentData?.data">
        <el-main>
          <el-upload
              action="#"
              :auto-upload="false"
              :show-file-list="false"
              :on-change="handleFileChange"
              accept=".json"
          >
            <template #trigger>
              <el-button type="primary" v-if="currentData?.app_id">
                <el-icon>
                  <Upload/>
                </el-icon>
                请上传JSON文件以开始编辑
              </el-button>
              <el-button type="danger" v-if="currentData?.app_id" @click.stop.prevent="handleDelete">删除</el-button>
            </template>
            <template #tip>
              <div class="upload-tip-container">
                <p class="text-md text-primary font-bold">如何从 ComfyUI 导出工作流(API格式)？</p>
                <p class="text-md text-secondary">首先启用 ComfyUI 设置中的 Dev Mode, 以显示导出 API
                  格式工作流的按钮</p>
                <img style="width: 592px; height: 125px" src="/@/assets/encryption/t.png"/>
                <p class="text-md text-secondary">随后将需要的工作流，以 API
                  格式的工作流保存,保存完毕后，即可上传工作流进行后续处理。</p>
                <div style="display: flex; justify-content: space-between; align-items: center;">
                  <div style="display: flex;flex-direction: column; align-items: center;">
                    <img style="width: 222px; height: 222px" src="/@/assets/encryption/bl.png"/>
                    <p class="text-sm text-secondary">旧版 ComfyUI 菜单栏点击 Save (API Format) 按钮导出</p>
                  </div>
                  <div style="display: flex;flex-direction: column; align-items: center;">
                    <img style="width: 222px; height: 222px" src="/@/assets/encryption/br.png"/>
                    <p class="text-sm text-secondary">新版 ComfyUI 菜单栏点击 Export (API) 按钮导出</p>
                  </div>
                </div>
              </div>
            </template>
          </el-upload>
        </el-main>
      </template>
      <template v-else>
        <div class="main-content-wrapper">
          <h3 class="content-title">所有可编辑节点<span class="sm-title">点击节点到右侧编辑</span> </h3>
          <el-scrollbar class="main-content-scrollbar">
            <el-collapse v-if="currentData &&  currentData.data && currentData.data.length !== 0"
                         class="encryption-collapse" expand-icon-position="left"
                         v-model="activeNames">
              <el-collapse-item v-for="(items,i) in currentData.data" :key="i" title="Consistency"
                                :name="items.title.number">
                <template #title>
                  <div>
                    {{ items.groupKey }}
                    <span v-if="items.alias">({{ items.alias }})</span>
                  </div>
                </template>
                <template #default>
                  <div v-for="(item, itemIndex) in items.inputs"
                       :key="itemIndex"
                       class="json-item"
                       :class="{ 'json-selected-item': item.editable }"
                       @click="handleLeftSelect(item,items)">
                    <div class="key-label key-label-left">
                      {{ item.field }}
                      <span v-if="item.alias">({{ item.alias }})</span>
                    </div>
                  </div>
                </template>
              </el-collapse-item>
            </el-collapse>
          </el-scrollbar>
        </div>
        <!-- 右侧边栏 -->
        <el-aside class="right-aside">
          <div class="info-section">
            <h3>应用介绍&输入建议</h3>
            <el-input
                v-model="currentData.intro"
                type="textarea"
                :rows="4"
                placeholder="请输入应用介绍和输入建议..."
                resize="none"
            />
          </div>
          <div class="media-section">
            <h3 class="content-title">已选择的修改节点<span class="sm-title">点击X取消编辑,并还原节点</span> </h3>
            <el-scrollbar>
              <el-collapse v-if="currentData &&  currentData.data && currentData.data.length !== 0"
                           class="encryption-collapse" expand-icon-position="left"
                           v-model="rightActiveNames">
                <template v-for="(items,i) in currentData.data" :key="i">
                  <el-collapse-item
                      v-if="items.inputs && items.inputs.some(input => input.editable)"
                      title="Consistency"
                      :name="items.title.number">
                    <template #title>
                      <div>
                        {{ items.groupKey }}
                        <span v-if="items.alias">({{ items.alias }})</span>
                      </div>
                    </template>
                    <template #default>
                      <template v-for="(item, itemIndex) in items.inputs" :key="itemIndex">
                        <div v-if="item.editable" class="json-item">
                          <div class="key-label">
                            {{ item.field }}
                            <span v-if="item.alias">({{ item.alias }})</span>
                            <el-icon class="alias-edit" @click.stop="editItemAlias(item)">
                              <edit/>
                            </el-icon>
                          </div>
                          <div class="value-input">
                            <div v-if="item.valueType === 'image'" class="image-upload-wrapper">
                              <el-button
                                  type="primary"
                                  size="small"
                                  @click.stop="triggerImageUpload(item)"
                                  class="upload-image-btn"
                              >
                                <el-icon><Upload /></el-icon>
                                上传图片
                              </el-button>
                            </div>
                            <el-input
                                v-model="item.value"
                                class="value-edit-btn"
                                :class="{ 'with-image-upload': item.valueType === 'image' }"
                            />
                          </div>
                          <div class="x-content" @click="handleValueChange(item)">
                            X
                          </div>
                        </div>
                      </template>
                    </template>
                  </el-collapse-item>
                </template>
              </el-collapse>
            </el-scrollbar>
            <div class="button-container">
              <el-button type="danger" @click="handleDelete">删除</el-button>
              <el-button type="primary" @click="handleSave">保存</el-button>
            </div>
          </div>
        </el-aside>
      </template>
    </el-container>
  </el-container>
</template>

<script setup>
import {Edit,Upload} from '@element-plus/icons-vue'
import {ElMessageBox, ElMessage} from 'element-plus'
import {useThrottle} from "/@/components/utils.ts";
// 左侧工作流列表数据
const workList = ref([])
const currentData = ref()
// 响应式数据
// const url = ref(window.location.origin);
const url = ref('https://1005801050051051521-http16539.east2.waas.aigate.cc');
// 添加获取文件名的方法
const getFileName = (filePath) => {
  if (!filePath) return '';
  const parts = filePath.split('/');
  return parts[parts.length - 1];
};

// 添加触发图片上传的方法
const triggerImageUpload = (item) => {
  const fileInput = document.createElement('input');
  fileInput.type = 'file';
  fileInput.accept = 'image/*';
  fileInput.style.display = 'none';

  fileInput.addEventListener('change', async (event) => {
    const file = event.target.files[0];
    if (file) {
      try {
        item.value = await uploadImageFile(file);
        ElMessage.success('图片上传成功');
      } catch (error) {
        ElMessage.error('图片上传失败: ' + error.message);
      }
    }
    // 清理input元素
    document.body.removeChild(fileInput);
  });

  document.body.appendChild(fileInput);
  fileInput.click();
};

// 上传图片文件到服务器
const uploadImageFile = (file) => {
  return new Promise((resolve, reject) => {
    const formData = new FormData();
    formData.append('file', file);

    fetch(url.value + '/api/file/upload-image', {
      method: 'POST',
      body: formData
    }).then(response => {
      if (!response.ok) {
        throw new Error('上传失败');
      }
      return response.json();
    })
    .then(data => {
      if(data.code === 200){
        const serverUrl = data.data.file_name;
        resolve(serverUrl)
        ElMessage.success('上传成功')
      }
      else
        ElMessage.error(data.message);
    })
    .catch(error => {
      reject(error);
    });
  });
};

const abstractData = ref('仅支持JSON格式文件，最大5MB');
const groupAliases = ref({});
// 方法定义
// 从接口获取左侧工作流数据
const fetchworkList = () => {
  fetch(url.value + '/api/app/list', {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json'
    },
  }).then(response => {
    if (!response.ok) {
      throw new Error('服务器响应错误');
    }
    return response.json();
  }).then(data => {
    if(data.code === 200){
      workList.value = data.data.list
      currentData.value = data.data.list[0]
      if(currentData.value){
        currentData.value.data = currentData.value.json_detail ? parseJsonData(currentData.value.json_detail, currentData.value):null;
      }
    }
    else
      ElMessage.error(data.message);

  }).catch(error => {
    ElMessage.error(error.message);
  })
}
const editItemName = useThrottle((item) => {
  ElMessageBox.prompt('请输入工作流名称', '编辑名称', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    inputValue: item.app_name,
    inputPattern: /^.{1,50}$/,
    inputErrorMessage: '名称不能为空且不能超过50个字符'
  }).then(({value}) => {
    // 这里可以调用API更新名称
    item.app_name = value;
    ElMessage.success('名称修改成功');
  }).catch(() => {
    // 用户取消操作
  });
}, 200);
const handleValueChange = useThrottle((item) => {
  item.editable = false;
  //item.value = originalData.value[item.node].inputs[item.field];
}, 200);
const addComfyApp = useThrottle(() => {
  fetch(url.value + '/api/app/create', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      app_name: '工作流',
    })
  }).then(response => {
    if (!response.ok) {
      throw new Error('服务器响应错误');
    }
    return response.json();
  }).then(data => {
    if(data.code === 200){
      ElMessage.success('添加成功')
      fetchworkList()
    }
    else
      ElMessage.error(data.message);

  }).catch(error => {
    ElMessage.error(error.message);
  })
}, 200)
// 更新工作流数据
const updateComfyApp = (id, data) => {
  fetch(url.value + '/api/app/update/' + id, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      json_detail:currentData.value.json_detail,
      ...data
    })
  }).then(response => {
    if (!response.ok) {
      throw new Error('服务器响应错误');
    }
    return response.json();
  }).then(data => {
    if(data.code === 200)
      ElMessage.success('保存成功')
    else
      ElMessage.error(data.message);
  }).catch(error => {
    ElMessage.error(error.message);
  })
}
const handleButtonClick = useThrottle((item) => {
  currentData.value = item;
  // 判断 item.json_detail 是字符串还是对象
  let jsonData;
  if (typeof item.json_detail === 'string') {
    // 如果是字符串，则尝试解析为 JSON 对象
    try {
      jsonData = JSON.parse(item.json_detail);
    } catch (error) {
      ElMessage.error('JSON解析错误：' + error.message);
      return;
    }
  } else if (typeof item.json_detail === 'object' && item.json_detail !== null) {
    // 如果已经是对象，直接使用
    jsonData = item.json_detail;
  } else {
    // 其他情况（如 null、undefined 等）设置为空对象或做其他处理
    jsonData = null;
  }

  if(jsonData){
    currentData.value.data = parseJsonData(jsonData, item);
  }else{
    currentData.value.data = null;
  }
}, 200);
const fullText = ref('')
const currentIndex = ref(0)
let typingTimer = null
const typeNextCharacter = () => {
  if (currentIndex.value < fullText.value.length) {
    abstractData.value += fullText.value[currentIndex.value]
    currentIndex.value++

    // 设置下一个字符的显示时间，可根据需要调整速度
    typingTimer = setTimeout(() => {
      typeNextCharacter()
    }, 150)
  } else {
    // 所有字符都已显示完毕
    typingTimer = null
  }
}
// 处理文件上传
const handleFileChange = (file) => {
  // 检查文件类型
  if (!file.raw.type.includes('json') && !file.name.endsWith('.json')) {
    ElMessage.error('请上传JSON格式文件！');
    return;
  }
  // 检查文件大小
  if (file.size > 5 * 1024 * 1024) {
    ElMessage.error('文件大小不能超过5MB！');
    return;
  }
  // 读取文件内容
  const reader = new FileReader();
  reader.onload = (e) => {
    try {
      currentData.value.json_detail  = e.target.result
      currentData.value.data = parseJsonData(JSON.parse(e.target.result))
    } catch (error) {
      ElMessage.error('JSON解析错误：' + error.message);
    }
  };
  reader.readAsText(file.raw);
};
// 左侧点击到右边
const handleLeftSelect = useThrottle((item,group) =>{
  item.editable = !item.editable
// 获取组的编号（用于匹配右侧面板）
  const groupName = group.title.number

  // 查找当前组是否在右侧有可编辑项
  const currentGroup = currentData.value.data.find(g => g.title.number === groupName)

  if (currentGroup) {
    // 检查当前组是否有任何可编辑项
    const hasEditableItems = currentGroup.inputs.some(input => input.editable)

    // 如果有可编辑项，展开该组；否则关闭该组
    if (hasEditableItems) {
      // 确保该组在 rightActiveNames 中
      if (!rightActiveNames.value.includes(groupName)) {
        rightActiveNames.value = [...rightActiveNames.value, groupName]
      }
    } else {
      // 从 rightActiveNames 中移除该组（关闭折叠面板）
      rightActiveNames.value = rightActiveNames.value.filter(name => name !== groupName)
    }
  }
}, 200)
const activeNames = ref([])
const rightActiveNames = ref([])
// 解析JSON数据
const parseJsonData = (originalJson,group) => {
  // 初始化结果结构
  const data = [];
  // 安全检查
  if (!originalJson) return data;
  // 遍历原始JSON的键值对（键为"3"、"4"等，值为节点信息）
  Object.entries(originalJson).forEach(([key, node]) => {
    // 提取节点核心信息
    const class_type = node?.class_type || "";
    const inputs = node?.inputs || {};
    let transformedInputs = []
    // 转换inputs结构：提取每个input的键作为field，alias为空
    let type = ''
    const imageExtensionRegex = /\.(jpg|jpeg|png|bmp|webp|tiff|tif|ico|svg)$/i;
    if(group){
      transformedInputs = Object.keys(inputs).map(inputKey => {
        const value = group['allow_input']?.[`${key}.inputs.${inputKey}`] || inputs[inputKey];
        return {
          field: inputKey,
          // 安全访问 alias
          alias: group['allow_input_alias']?.[`${key}.inputs.${inputKey}`] || "",
          // 安全访问 value
          value: value,
          // 安全访问 editable
          editable: !!group['allow_input']?.[`${key}.inputs.${inputKey}`],
          valueType:(typeof value === 'string' && imageExtensionRegex.test(value))?'image':''
        };
      }).filter(input => {
        // 过滤掉value不是string、number或boolean的内容
        const type = typeof input.value;
        return type === 'string' || type === 'number' || type === 'boolean';
      });
    }else{
      transformedInputs = Object.keys(inputs).map(inputKey => {
        const value = inputs[inputKey];
        return {
          field: inputKey,
          alias: "",
          value: value,
          valueType:(typeof value === 'string' && imageExtensionRegex.test(value))?'image':''
        };
      }).filter(input => {
        // 过滤掉value不是string、number或boolean的内容
        const type = typeof input.value;
        return type === 'string' || type === 'number' || type === 'boolean';
      });
    }
    // 构造当前节点的配置
    const collapseItem = {
      groupKey: `${key}-${class_type}`, // 格式：键-class_type（如"3-KSampler"）
      alias: "", // 按需求，items.alias为空
      title: {
        number: key // title.number为原始键（如"3"）
      },
      inputs: transformedInputs // 转换后的inputs数组
    };
    activeNames.value.push(key);
    rightActiveNames.value.push(key);
    // 添加到结果数组
    data.push(collapseItem);
  });
  return data;
}
// 添加删除和保存处理函数
const handleDelete = useThrottle(() => {
  ElMessageBox.confirm('确定要删除当前应用吗？此操作不可恢复。', '删除确认', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(() => {
    // 执行删除操作
    fetch(url.value + '/api/app/delete/' + currentData.value.app_id, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      }
    }).then(response => {
      if (!response.ok) {
        throw new Error('删除失败');
      }
      return response.json();
    }).then(data => {
      if(data.code === 200){
        ElMessage.success('删除成功');
        // 重新获取列表
        fetchworkList();
      } else
        ElMessage.error(data.message);
    }).catch(error => {
      ElMessage.error(error.message);
    });
  }).catch(() => {
    // 用户取消删除
  });
}, 200);

const handleSave = useThrottle(() => {
  // 执行保存操作
  const saveData = {
    app_name: currentData.value.app_name,
    intro: currentData.value.intro,
    ...transformData()
  };
  updateComfyApp(currentData.value.app_id, saveData);
}, 200);
const transformData = () => {
  const data = currentData.value?.data || [];
  const allow_input = {};
  const allow_input_alias = {};

  // 空数据保护
  if (!data?.length) {
    return { allow_input, allow_input_alias };
  }

  data.forEach(groupItem => {
    // 安全提取分组前缀
    const groupKey = groupItem?.groupKey || "";
    const groupPrefix = groupKey.split('-')[0] || "";

    // 安全遍历输入项
    const inputs = groupItem?.inputs || [];
    inputs.forEach(inputItem => {
      // 安全构造键路径
      const field = inputItem?.field || "";
      const keyPath = `${groupPrefix}.inputs.${field}`;

      // 安全处理value
      if (inputItem?.editable && inputItem?.value !== undefined) {
        allow_input[keyPath] = inputItem.value;
      }

      // 安全处理alias
      if (inputItem?.editable && inputItem?.alias && inputItem.alias.trim() !== '') {
        allow_input_alias[keyPath] = inputItem.alias;
      }
    });
  });

  return { allow_input, allow_input_alias };
}

const editItemAlias = useThrottle((item) => {
  ElMessageBox.prompt('请输入节点别名', '节点别名', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    inputValue: item.alias,
    inputPattern: /^.{0,20}$/,
    inputErrorMessage: '别名不能超过20个字符'
  }).then(({value}) => {
    item.alias = value;
  }).catch(() => {
  });
}, 200);

const editGroupAlias = (group) => {
  ElMessageBox.prompt('请输入分组别名', '分组别名', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    inputValue: groupAliases.value[group.groupKey] || '', // 获取当前别名
    inputPattern: /^.{0,20}$/,
    inputErrorMessage: '别名不能超过20个字符'
  }).then(({value}) => {
    // 更新响应式数据源
    groupAliases.value[group.groupKey] = value;
  }).catch(() => {
  });
};
// 组件挂载时获取左侧工作流数据
onMounted(() => {
  fetchworkList()
})
</script>

<style>
.json-item {overflow: hidden;   display: flex; margin-bottom: 5px; padding:0 12px; background: #f8f9ff; border-radius: 8px; transition: all 0.3s ease;cursor: pointer; }
.json-item:hover { background: linear-gradient(90deg, #CAD5FA 0%, #B1C0F0 80%); transform: translateY(-2px); box-shadow: 0 4px 8px rgba(106, 17, 203, 0.1); }
.key-label { flex: 1; font-weight: 600; color: #2c3e50; display: flex; align-items: center; font-size: 0.95rem; }
.json-selected-item{
  background: linear-gradient(90deg, #CAD5FA 0%, #B1C0F0 80%);
  box-shadow: 0 3px 12px rgba(106, 127, 240, 0.12);
  transform: translateY(-2px);
}
.key-label-left{
  flex: 1;
  font-weight: 600;
  color: #2c3e50;
  display: flex;
  align-items: center;
  font-size: 0.95rem;
  overflow: hidden;          /* 隐藏溢出内容 */
  text-overflow: ellipsis;   /* 超出部分显示省略号 */
  white-space: nowrap;       /* 不换行 */
}
.alias-edit{
  margin-left: 4px;
}
.value-input{
  display: flex;
  align-items: center;
}
.value-edit-btn{
  width: 240px !important;
}
.sidebar-button-active {
  background-color: #409eff !important;
  color: white !important;
}

.sidebar-button-active .button-text {
  color: white !important;
}

.sidebar-button-active .edit-icon {
  color: white !important;
}
.content-title{
  padding: 5px 10px;
}
.sm-title { font-size: 12px; color: #9fa5b0; margin-left: 15px;margin-top: 5px; }
/* 添加按钮容器样式 */
.button-container {
  position: fixed;
  bottom: 20px;
  right: 20px;
  display: flex;
  gap: 10px;
  padding: 10px 20px;
  background-color: #f5f5f5;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  >button{
    width: 150px;
  }
}
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

body {
  padding: 0;
  margin: 0;
  height: 100vh;
  overflow: hidden;
  background-color: #f5f5f5; /* 页面背景色改为灰色 */
}


.full-container {
  height: 100vh;
  background-color: #f5f5f5; /* 容器背景色也改为灰色 */
}


.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  color: white;
  border-bottom: 1px solid #ddd; /* 顶部添加边框 */
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  z-index: 10;
}

.logo-container {
  display: flex;
  align-items: center;
  width: 200px;
  justify-content: center;

  img {
    width: 112px;
  }
}

.header-title {
  flex: 1;
  font-size: 20px;
  font-weight: 600;
  text-align: center;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  color: #002758;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  letter-spacing: 1px;
  padding-right: 200px;
}

.logo {
  height: 40px;
  width: auto;
}


/* 左侧边栏样式 */
.left-aside {
  background-color: #f5f5f5;
  padding: 20px 0;
  overflow: hidden;
  height: calc(100vh - 60px);
}

.button-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.sidebar-button-first {
  margin: 0 10px;
  text-align: center;
}

.sidebar-button {
  margin: 0 10px;
  text-align: left;

  > span {
    flex: 1;
    width: 190px;
  }
}

.button-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.text-container {
  flex: 1;
  min-width: 0; /* 允许 flex 项收缩到小于其内容的宽度 */
  margin-right: 8px;
  text-align: left;
  width: 165px;
}

.button-text {
  display: inline-block;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.edit-icon {
  font-size: 14px;
  cursor: pointer;
  color: #909399;
  transition: color 0.3s;
  flex-shrink: 0; /* 防止图标被压缩 */
}

.edit-icon:hover {
  color: #409eff;
}

/* 主内容区域样式 */
.main-content-wrapper {
  display: flex;
  flex-direction: column;
  position: relative;
  margin: 10px;
  flex: 2;
  overflow: hidden;
  height: calc(100vh - 80px);
  background-color: #fff;
  border-radius: 5px;
  padding: 5px;
}
.el-collapse-item__wrap {
  border: none !important;
}
.main-content-wrapper .el-collapse-item__content{
  padding: 5px 8px;
  padding-bottom: 5px !important;
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 10px;
}
.main-content-wrapper .json-item{
  height: 34px;
}
.el-collapse{
  border: none !important;
}
.el-collapse-item__header{
  border: none !important;
}
/* 右侧边栏样式 */
.right-aside {
  background-color: #f5f5f5;
  padding: 20px;
  display: flex;
  flex-direction: column;
  flex: 3;
  height: calc(100vh - 60px);
}
.media-section{
  height: calc(100vh - 230px);
  background-color: #fff;
  overflow: hidden;
  padding-bottom: 70px;
}
.right-aside .el-collapse-item__content{
  padding: 5px 8px;
  padding-bottom: 5px !important;
}
.right-aside .json-item{
  height: 50px;
  margin-bottom: 8px;
  align-items: center;
}
.x-content{
  display: flex;
  justify-content: center; /* 水平居中 */
  align-items: center;     /* 垂直居中 */
  width: 40px;
  cursor: pointer;
  color: #c03639;
}
.info-section {
  margin-bottom: 20px;
  padding: 15px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  background-color: #fff;
}

.media-section {
  flex: 1;
}

.upload-tip-container {
  width: 610px;
  margin-left: 0;
  margin-top: 24px;
  margin-bottom: 24px;
}

.text-md {
  font-size: 14px;
}

.text-primary {
  color: #002758;
}

.font-bold {
  font-weight: bold;
}

.text-secondary {
  color: #9FA5B0;
  margin-top: 13px;
  margin-bottom: 13px;
}

.text-sm {
  font-size: 12px;
}
.image-upload-wrapper {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-right: 5px;
}

.upload-image-btn {
  height: 32px;
}

.image-preview-text {
  font-size: 12px;
  color: #67c23a;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>