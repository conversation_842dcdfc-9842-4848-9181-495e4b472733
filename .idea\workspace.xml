<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="70fdeed7-a579-47c2-b7e3-1399914c9a12" name="更改" comment="8.7">
      <change afterPath="$PROJECT_DIR$/runApp/src/components/trae-image-mask.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/runApp/src/components/vue-image-mask.vue" beforeDir="false" afterPath="$PROJECT_DIR$/runApp/src/components/vue-image-mask.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/runApp/src/views/workflow.vue" beforeDir="false" afterPath="$PROJECT_DIR$/runApp/src/views/workflow.vue" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="PackageJsonUpdateNotifier">
    <dismissed value="$PROJECT_DIR$/makeApp/package.json" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 1
}</component>
  <component name="ProjectId" id="30IpAupaB8f0pSKtfjhC62CugH4" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "ModuleVcsDetector.initialDetectionPerformed": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.git.unshallow": "true",
    "codeReviewSummary": "[]",
    "git-widget-placeholder": "master",
    "last_opened_file_path": "D:/code/makeApp/runApp/src/components",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "npm.build (1).executor": "Run",
    "npm.build.executor": "Run",
    "npm.dev (1).executor": "Run",
    "npm.dev.executor": "Run",
    "ts.external.directory.path": "D:\\code\\makeApp\\makeApp\\node_modules\\typescript\\lib",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="D:\code\makeApp\runApp\src\components" />
      <recent name="D:\code\makeApp\makeApp\public" />
      <recent name="D:\code\makeApp\runApp" />
      <recent name="D:\code\makeApp\runApp\public" />
      <recent name="D:\code\makeApp\runApp\src\assets" />
    </key>
  </component>
  <component name="RunManager" selected="npm.build (1)">
    <configuration name="build (1)" type="js.build_tools.npm" temporary="true" nameIsGenerated="true">
      <package-json value="$PROJECT_DIR$/runApp/package.json" />
      <command value="run" />
      <scripts>
        <script value="build" />
      </scripts>
      <node-interpreter value="project" />
      <envs />
      <method v="2" />
    </configuration>
    <configuration name="build" type="js.build_tools.npm" temporary="true" nameIsGenerated="true">
      <package-json value="$PROJECT_DIR$/makeApp/package.json" />
      <command value="run" />
      <scripts>
        <script value="build" />
      </scripts>
      <node-interpreter value="project" />
      <envs />
      <method v="2" />
    </configuration>
    <configuration name="dev (1)" type="js.build_tools.npm" temporary="true" nameIsGenerated="true">
      <package-json value="$PROJECT_DIR$/runApp/package.json" />
      <command value="run" />
      <scripts>
        <script value="dev" />
      </scripts>
      <node-interpreter value="project" />
      <envs />
      <method v="2" />
    </configuration>
    <configuration name="dev" type="js.build_tools.npm" temporary="true" nameIsGenerated="true">
      <package-json value="$PROJECT_DIR$/makeApp/package.json" />
      <command value="run" />
      <scripts>
        <script value="dev" />
      </scripts>
      <node-interpreter value="project" />
      <envs />
      <method v="2" />
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="npm.build (1)" />
        <item itemvalue="npm.build" />
        <item itemvalue="npm.dev (1)" />
        <item itemvalue="npm.dev" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-js-predefined-d6986cc7102b-f27c65a3e318-JavaScript-WS-251.23774.424" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="70fdeed7-a579-47c2-b7e3-1399914c9a12" name="更改" comment="" />
      <created>1753324801615</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1753324801615</updated>
      <workItem from="1753324802738" duration="3004000" />
      <workItem from="1753405005991" duration="4502000" />
      <workItem from="1753423530608" duration="8474000" />
      <workItem from="1753664041461" duration="821000" />
      <workItem from="1753750743096" duration="6946000" />
      <workItem from="1753836888661" duration="3635000" />
      <workItem from="1754009852093" duration="881000" />
      <workItem from="1754528004047" duration="13660000" />
      <workItem from="1754614506077" duration="18350000" />
    </task>
    <task id="LOCAL-00001" summary="8.7">
      <option name="closed" value="true" />
      <created>1754530617867</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1754530617867</updated>
    </task>
    <task id="LOCAL-00002" summary="8.7">
      <option name="closed" value="true" />
      <created>1754532686249</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1754532686249</updated>
    </task>
    <task id="LOCAL-00003" summary="8.7">
      <option name="closed" value="true" />
      <created>1754555661560</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1754555661560</updated>
    </task>
    <task id="LOCAL-00004" summary="8.7">
      <option name="closed" value="true" />
      <created>1754620132222</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1754620132222</updated>
    </task>
    <option name="localTasksCounter" value="5" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="8.7" />
    <option name="LAST_COMMIT_MESSAGE" value="8.7" />
  </component>
</project>